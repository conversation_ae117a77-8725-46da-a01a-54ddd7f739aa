const app = getApp()

Page({
  data: {
    productsList: [],
    hasUserInfo: false,
    hasCompanyCode: false,
    companyCode: '',
    loading: true,
    companyName: '', // 公司名称
    productsShareImage: '', // 产品中心分享图片
    externalUrl: '', // 存储外部URL
    cardId: null,     // 存储来自分享链接的卡片ID
    fromCardDetail: false, // 标记是否来自名片详情页
    fromSharedCard: false,  // 标记是否来自分享的名片
    productsName: '产品中心', // 添加 productsName 字段
    fromShare: false,
    fromPersonal: false, // 标记是否来自个人页面
    fromCardDetail: false, // 标记是否来自名片详情页
    shareProductId: null,
    shareProductName: null,
    shareProductImage: null,
    shareProductUrl: null,
    isNavCollapsed: false, // 导航栏默认展开
    showNavBar: false,     // 默认不显示导航栏
    companyInfo: null,     // 企业信息
    customName: '新页面'   // 自定义页面名称
  },

  onLoad(options) {
    console.log('产品中心页面加载参数:', options);
    
    // 判断是否需要显示导航栏 - 现在所有情况都显示导航栏
    let shouldShowNavBar = true;

    // 保存来源信息
    if (options.fromShare === 'true') {
      this.setData({
        fromShare: true
      });
    }

    // 如果来自个人页面，设置标记并显示导航栏
    if (options.fromPersonal === 'true') {
      this.setData({
        fromPersonal: true
      });
      console.log('来自个人页面，显示导航栏');
    }

    // 如果来自名片详情页，设置标记
    if (options.fromCardDetail === 'true') {
      this.setData({
        fromCardDetail: true
      });
      console.log('来自名片详情页');
    }

    // 保存卡片ID（如果有）
    if (options.cardId) {
      this.setData({
        cardId: options.cardId
      });
      console.log('设置cardId:', options.cardId);
    }

    // 设置是否显示导航栏
    this.setData({
      showNavBar: shouldShowNavBar
    });
    
    // 保存产品ID（如果有）
    if (options.productId) {
      this.setData({
        shareProductId: options.productId
      });
    }
    
    // 如果是从名片详情页跳转过来，先尝试获取该名片的企业信息
    if (this.data.fromCardDetail && options.cardId) {
      console.log("从名片详情页跳转，cardId:", options.cardId);
      this.loadCardCompanyInfo(options.cardId);
      return;
    }

    // 检查是否指定了companyCode
    if (options.companyCode) {
      this.setData({
        companyCode: options.companyCode,
        hasCompanyCode: true
      });

      // 加载企业信息（包括自定义名称和分享图片）
      this.loadCompanyInfo(options.companyCode).then(() => {
        // 加载企业产品
        this.loadProducts(options.companyCode);
      }).catch(err => {
        console.error('加载企业信息失败:', err);
        // 即使企业信息加载失败，也尝试加载产品列表
        this.loadProducts(options.companyCode);
      });
    } else {
      // 检查登录状态
      this.checkLoginStatus();
    }
  },

  onShow() {
    // 如果需要，可以在这里刷新数据
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2 // 选中产品中心页面
      });
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    // 获取存储的token
    const token = wx.getStorageSync('token');
    
    if (token) {
      this.setData({
        hasUserInfo: true
      });
      // 获取用户关联的企业信息
      this.getUserCompanyInfo();
    } else {
      this.setData({
        loading: false
      });
      // 未登录状态下，显示登录提示
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 获取用户关联的企业信息
  getUserCompanyInfo() {
    const company = require('../../utils/company.js');
    company.getCompanyInfo()
      .then(res => {
        // 添加日志，帮助调试
        console.log('获取到的企业信息:', res);
        
        // 检查返回的是标准响应格式还是直接的企业信息对象
        let companyData = res;
        
        // 如果是标准响应格式（有code和data字段）
        if (res && res.code === 0 && res.data) {
          companyData = res.data;
        }
        
        // 如果获取到有效的企业信息
        if (companyData && companyData.companyCode) {
          // 确保productsShareImage字段存在，如果不存在则使用默认图片
          const productsShareImage = companyData.productsShareImage || '';
          console.log('设置产品中心分享图片:', productsShareImage);
          
          this.setData({
            companyCode: companyData.companyCode,
            hasCompanyCode: true,
            companyName: companyData.name || '',
            productsShareImage: productsShareImage,
            productsName: companyData.productsName || '产品中心',
            companyInfo: companyData,
            customName: companyData.customName || '新页面'
          });
          
          // 设置页面标题为自定义产品中心名称
          if (companyData.productsName) {
            wx.setNavigationBarTitle({
              title: companyData.productsName
            });
          }
          
          // 加载企业产品
          this.loadProducts(companyData.companyCode);
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: '您尚未关联企业',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('获取企业信息失败', err);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '获取企业信息失败',
          icon: 'none'
        });
      });
  },

  // 加载产品列表
  loadProducts(companyCode) {
    this.setData({
      loading: true
    });
    
    // 确保API基础URL存在
    const apiBaseUrl = app.globalData.baseUrl || 'https://zl.sdtaa.com/api/v1';
    console.log('使用API基础URL:', apiBaseUrl);
    
    // 准备请求参数
    const requestData = {
      companyCode: companyCode
    };
    
    // 如果有特定产品ID，添加到请求参数
    if (this.data.shareProductId) {
      requestData.productId = this.data.shareProductId;
      console.log('添加产品ID到请求参数:', this.data.shareProductId);
    }
    
    wx.request({
      url: `${apiBaseUrl}/products`,
      method: 'GET',
      data: requestData,
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        if (res.data.code === 0) {
          console.log('加载产品列表成功，数据:', res.data.data.list);
          
          // 获取产品列表
          const productsList = res.data.data.list || [];
          
          // 如果有特定产品ID，尝试找到该产品并设置分享参数
          if (this.data.shareProductId && productsList.length > 0) {
            const targetProduct = productsList.find(item => item.id == this.data.shareProductId);
            if (targetProduct) {
              console.log('找到目标产品:', targetProduct);
              this.setData({
                shareProductName: targetProduct.name,
                shareProductImage: targetProduct.shareImage || targetProduct.coverImage,
                shareProductUrl: targetProduct.productUrl
              });
            }
          }
          
          this.setData({
            productsList: productsList,
            loading: false
          });
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: res.data.message || '加载产品失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求产品列表失败', err);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 查看产品详情
  viewProduct(e) {
    const productId = e.currentTarget.dataset.id;
    const productUrl = e.currentTarget.dataset.url;
    const productName = e.currentTarget.dataset.name || ''; // 获取产品名称
    const productImage = e.currentTarget.dataset.image || ''; // 获取产品图片
    
    if (productUrl) {
      // 如果有产品URL，直接跳转，并添加fromProductCenter=true参数
      let url = `/pages/products/detail?url=${encodeURIComponent(productUrl)}&fromProductCenter=true`;
      
      // 添加企业代码
      if (this.data.companyCode) {
        url += `&companyCode=${this.data.companyCode}`;
      }
      
      // 如果是从分享进入的，保留cardId参数
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }
      
      // 如果本身是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }
      
      // 添加产品名称和图片信息
      if (productName) {
        url += `&productName=${encodeURIComponent(productName)}`;
      }
      
      if (productImage) {
        url += `&productImage=${encodeURIComponent(productImage)}`;
      }
      
      console.log('跳转到产品详情页，携带参数:', url);
      
      wx.navigateTo({
        url: url
      });
    } else {
      wx.showToast({
        title: '产品链接未设置',
        icon: 'none'
      });
    }
  },

  // 分享特定产品
  shareProduct(e) {
    const productId = e.currentTarget.dataset.id;
    const productName = e.currentTarget.dataset.name;
    const productImage = e.currentTarget.dataset.image;  // 这已经是经过优先级处理的图片路径(shareImage || coverImage)
    const productUrl = e.currentTarget.dataset.url;  // 获取产品URL
    
    console.log('准备分享产品:', {
      productId: productId,
      productName: productName,
      productImage: productImage,
      productUrl: productUrl  // 记录产品URL
    });
    
    // 设置分享参数
    this.setData({
      shareProductId: productId,
      shareProductName: productName,
      shareProductImage: productImage,
      shareProductUrl: productUrl  // 保存产品URL
    });
    
    // 使用统一分享模块调起分享菜单
    const share = require('../../utils/share');
    share.handleShareButtonClick();
  },

  // 分享产品中心
  shareProductCenter() {
    // 清除特定产品分享信息，确保分享整个产品中心
    this.setData({
      shareProductId: null,
      shareProductName: null,
      shareProductImage: null,
      shareProductUrl: null
    });
    
    console.log('分享产品中心页面，企业名称:', this.data.companyName);
    console.log('分享产品中心页面，分享图片:', this.data.productsShareImage);
    
    // 使用统一分享模块
    const share = require('../../utils/share');
    share.handleShareButtonClick();
  },

  // 分享功能
  onShareAppMessage(options) {
    // 确保有cardId，如果没有则从全局数据中获取
    let cardId = this.data.cardId;
    if (!cardId && app.globalData.cardInfo && app.globalData.cardInfo.id) {
      cardId = app.globalData.cardInfo.id;
      console.log("从全局数据获取cardId:", cardId);
    }

    // 添加日志，帮助调试
    console.log('分享信息:', {
      companyName: this.data.companyName,
      companyCode: this.data.companyCode,
      shareProductId: this.data.shareProductId,
      shareProductName: this.data.shareProductName,
      shareProductImage: this.data.shareProductImage,
      shareProductUrl: this.data.shareProductUrl,
      productsShareImage: this.data.productsShareImage,
      productsName: this.data.productsName,
      cardId: cardId,
      fromPersonal: this.data.fromPersonal
    });
    
    // 使用统一分享模块
    const share = require('../../utils/share');
    
    // 如果是分享特定产品
    if (this.data.shareProductId) {
      // 检查是否有产品URL
      let productUrl = this.data.shareProductUrl;
      
      // 如果没有直接的URL，尝试从产品列表中查找
      if (!productUrl) {
        const targetProduct = this.data.productsList.find(item => item.id == this.data.shareProductId);
        if (targetProduct && targetProduct.productUrl) {
          productUrl = targetProduct.productUrl;
        }
      }
      
      // 如果找到了产品URL，分享产品详情页
      if (productUrl) {
        // 始终确保有cardId和companyCode
        const shareData = {
          productUrl: productUrl,
          productName: this.data.shareProductName || '产品详情',
          productImage: this.data.shareProductImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png',
          cardId: cardId,
          companyCode: this.data.companyCode,
          fromPersonal: this.data.fromPersonal // 传递来自个人页面的标识
        };
        
        console.log('分享产品详情，数据:', shareData);
        
        return share.handleShare({
          type: 'product',
          data: shareData
        });
      }
      
      // 如果没有产品URL，分享到产品中心并带上产品ID
      let path = `/pages/products/index?productId=${this.data.shareProductId}&companyCode=${this.data.companyCode}&fromShare=true${cardId ? '&cardId=' + cardId : ''}`;

      // 如果来自个人页面，添加fromPersonal参数
      if (this.data.fromPersonal) {
        path += '&fromPersonal=true';
      }
      
      return {
        title: `${this.data.shareProductName || '产品详情'}`,
        path: path,
        imageUrl: this.data.shareProductImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png'
      };
    }
    
    // 默认分享整个产品中心
    const productData = {
      cardInfo: {
        id: cardId
      },
      companyCode: this.data.companyCode,
      productsName: this.data.productsName,
      productsShareImage: this.data.productsShareImage,
      fromPersonal: this.data.fromPersonal // 传递来自个人页面的标识
    };
    
    console.log('分享产品中心，数据:', productData);
    
    return share.handleShare({
      type: 'products',
      data: productData
    });
  },

  // 前往编辑名片页面
  goToEditCard() {
    wx.navigateTo({
      url: '/pages/personal/edit'
    });
  },

  // 返回名片页面
  goBackToCard() {
    if (this.data.cardId) {
      wx.navigateTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}`,
        fail: (err) => {
          console.error("跳转到名片页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: `/pages/card-detail/index?id=${this.data.cardId}`
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到原始名片',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 返回个人页面
  goBackToPersonal() {
    wx.switchTab({
      url: '/pages/personal/index'
    });
  },

  // 跳转到企业页面
  goToEnterprise() {
    if (this.data.companyCode) {
      let url = `/pages/enterprise/index?companyCode=${this.data.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到企业页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 跳转到自定义页面
  goToCustomPage() {
    if (this.data.companyCode) {
      let url = `/pages/custom/index?companyCode=${this.data.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到自定义页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 加载企业信息
  loadCompanyInfo(companyCode) {
    const company = require('../../utils/company.js');
    return company.getCompanyInfo(companyCode)
      .then(res => {
        // 添加日志，帮助调试
        console.log('获取到的企业信息:', res);
        
        // 检查返回的是标准响应格式还是直接的企业信息对象
        let companyData = res;
        
        // 如果是标准响应格式（有code和data字段）
        if (res && res.code === 0 && res.data) {
          companyData = res.data;
        }
        
        // 如果获取到有效的企业信息
        if (companyData && companyData.companyCode) {
          // 确保productsShareImage字段存在，如果不存在则使用默认图片
          const productsShareImage = companyData.productsShareImage || '';
          console.log('设置产品中心分享图片:', productsShareImage);
          
          this.setData({
            companyCode: companyData.companyCode,
            hasCompanyCode: true,
            companyName: companyData.name || '',
            productsShareImage: productsShareImage,
            productsName: companyData.productsName || '产品中心',
            companyInfo: companyData,
            customName: companyData.customName || '新页面'
          });
          
          // 设置页面标题为自定义产品中心名称
          if (companyData.productsName) {
            wx.setNavigationBarTitle({
              title: companyData.productsName
            });
          }
          
          return companyData;
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: '您尚未关联企业',
            icon: 'none',
            duration: 2000
          });
          return null;
        }
      })
      .catch(err => {
        console.error('获取企业信息失败', err);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '获取企业信息失败',
          icon: 'none'
        });
        throw err;
      });
  },

  // 根据名片ID加载企业信息
  loadCardCompanyInfo(cardId) {
    console.log("开始根据名片ID加载企业信息:", cardId);

    this.setData({
      loading: true
    });

    const card = require('../../utils/card.js');
    card.getCardById(cardId).then(res => {
      console.log("获取名片详情返回:", res);

      if (res.code === 0 && res.data) {
        // 处理字段名不一致的问题
        const cardData = res.data;
        // 检查是否有company_code字段但没有companyCode字段的情况
        if (cardData.company_code !== undefined && cardData.companyCode === undefined) {
          cardData.companyCode = cardData.company_code;
          console.log("从company_code转换为companyCode:", cardData.companyCode);
        }

        if (cardData.companyCode) {
          console.log("获取到名片的企业编码:", cardData.companyCode);

          // 设置企业代码
          this.setData({
            companyCode: cardData.companyCode,
            hasCompanyCode: true
          });

          // 根据名片的企业编码获取企业信息
          this.loadCompanyInfo(cardData.companyCode).then(() => {
            // 加载企业产品
            this.loadProducts(cardData.companyCode);
          }).catch(err => {
            console.error("获取企业信息失败:", err);
            this.setData({
              loading: false
            });
          });
        } else {
          console.log("名片没有关联企业编码");
          this.setData({
            hasCompanyCode: false,
            loading: false
          });
        }
      } else {
        console.log("获取名片信息失败:", res);
        this.setData({
          loading: false
        });
      }
    }).catch(err => {
      console.error("获取名片失败:", err);
      this.setData({
        loading: false
      });
    });
  },

  // 显示导航栏
  showNav() {
    console.log('显示导航栏');
    this.setData({
      isNavCollapsed: false
    });
  },
  
  // 隐藏导航栏
  hideNav() {
    console.log('隐藏导航栏');
    this.setData({
      isNavCollapsed: true
    });
  }
}) 