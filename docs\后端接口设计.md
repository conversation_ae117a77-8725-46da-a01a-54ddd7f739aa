# 智链小程序后端接口设计

## 基础信息

- 基础URL: `https://api.zhilian.com/v1`
- 所有接口返回格式: JSON
- 验证方式: Token认证 (请求头 `Authorization: Bearer {token}`)

## 通用响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 具体的响应数据
  }
}
```

### 失败响应

```json
{
  "code": 错误代码,
  "message": "错误描述",
  "data": null
}
```

### 通用错误代码

- 400: 请求参数错误
- 401: 未授权或token过期
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 详细接口设计

### 1. 用户认证模块

#### 1.1 微信登录

- **接口**: `POST /auth/login`
- **描述**: 使用微信临时登录凭证code换取用户登录态
- **请求参数**:

  ```json
  {
    "code": "微信临时登录凭证code"
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "token": "jwt_token...",
      "userId": 1,
      "openid": "用户openid",
      "companyCode": "COMPANY123", // 企业唯一标识码
      "aiAvatarUrl": "https://example.com/ai-avatar", // AI分身URL，可空
      "hasCardInfo": true // 是否有名片信息
    }
  }
  ```

#### 1.2 获取用户信息

- **接口**: `GET /auth/user`
- **描述**: 获取当前登录用户信息
- **请求参数**: 无需额外参数，通过token识别用户
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "userId": 1,
      "openid": "用户openid",
      "companyCode": "COMPANY123",
      "aiAvatarUrl": "https://example.com/ai-avatar", // AI分身URL，可空
      "hasCardInfo": true
    }
  }
  ```

### 2. 名片管理模块

#### 2.1 获取当前用户名片信息

- **接口**: `GET /card`
- **描述**: 获取当前登录用户的名片信息
- **请求参数**: 无需额外参数，通过token识别用户
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "avatar": "https://example.com/avatar.jpg",
      "name": "张三",
      "company": "智链科技有限公司",
      "position": "技术总监",
      "industry": "互联网/软件",
      "mobile": "13800138000",
      "wechat": "zhangsan",
      "email": "<EMAIL>",
      "phone": "010-12345678",
      "website": "https://www.example.com",
      "address": "北京市朝阳区xxx大厦",
      "introduction": "专注于微信小程序开发...",
      "companyCode": "COMPANY123",
      "aiAvatarUrl": "https://example.com/ai-avatar", // AI分身URL，可空
      "createdAt": "2023-05-01T08:00:00Z",
      "updatedAt": "2023-05-10T10:30:00Z"
    }
  }
  ```

#### 2.2 创建/更新用户名片

- **接口**: `POST /card`
- **描述**: 创建或更新当前用户的名片信息
- **请求参数**:

  ```json
  {
    "avatar": "https://example.com/avatar.jpg", // 选填
    "name": "张三", // 必填
    "company": "智链科技有限公司", // 必填
    "position": "技术总监", // 选填
    "industry": "互联网/软件", // 选填
    "mobile": "13800138000", // 选填
    "wechat": "zhangsan", // 选填
    "email": "<EMAIL>", // 选填
    "phone": "010-12345678", // 选填
    "website": "https://www.example.com", // 选填
    "address": "北京市朝阳区xxx大厦", // 选填
    "introduction": "专注于微信小程序开发..." // 选填
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "avatar": "https://example.com/avatar.jpg",
      "name": "张三",
      "company": "智链科技有限公司",
      "position": "技术总监",
      "industry": "互联网/软件",
      "mobile": "13800138000",
      "wechat": "zhangsan",
      "email": "<EMAIL>",
      "phone": "010-12345678",
      "website": "https://www.example.com",
      "address": "北京市朝阳区xxx大厦",
      "introduction": "专注于微信小程序开发...",
      "companyCode": "COMPANY123", // 系统自动生成或匹配的企业编码
      "createdAt": "2023-05-01T08:00:00Z",
      "updatedAt": "2023-05-15T14:30:00Z"
    }
  }
  ```

#### 2.3 获取指定ID的名片信息

- **接口**: `GET /card/:id`
- **描述**: 获取指定ID的名片信息，用于查看他人名片
- **路径参数**: id - 名片ID
- **权限**: 支持未登录用户访问，但登录用户可获取更多信息
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 2,
      "avatar": "https://example.com/avatar2.jpg",
      "name": "李四",
      "company": "某某科技有限公司",
      "position": "产品经理",
      "industry": "互联网/软件",
      "mobile": "13900139000",
      "wechat": "lisi",
      "email": "<EMAIL>",
      "phone": "021-12345678",
      "website": "https://www.lisi-company.com",
      "address": "上海市浦东新区xxx广场",
      "introduction": "专注于产品设计与用户体验...",
      "companyCode": "COMPANY456",
      "aiAvatarUrl": "https://example.com/lisi-ai-avatar", // AI分身URL，可空
      "isCollected": false, // 当前用户是否已收藏该名片
      "createdAt": "2023-04-15T08:00:00Z",
      "updatedAt": "2023-05-05T10:30:00Z"
    }
  }
  ```

#### 2.4 检查用户名片状态

- **接口**: `GET /card/check-status`
- **描述**: 检查当前用户是否拥有名片信息
- **请求参数**: 无需额外参数，通过token识别用户
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "isLoggedIn": true,
      "hasCardInfo": true,
      "cardInfo": {
        // 如果有名片信息，则包含完整名片数据
        "id": 1,
        "avatar": "https://example.com/avatar.jpg",
        "name": "张三",
        // ...其他名片字段
      }
    }
  }
  ```

#### 2.5 获取用户的所有名片

- **接口**: `GET /api/card/all`
- **描述**: 获取当前登录用户的所有名片
- **请求参数**: 无需额外参数，通过token识别用户
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "list": [
        {
          "id": 1,
          "avatar": "https://example.com/avatar.jpg",
          "name": "张三",
          "company": "智链科技有限公司",
          "position": "技术总监",
          "isDefault": true,
          "createdAt": "2023-05-01T08:00:00Z",
          "updatedAt": "2023-05-10T10:30:00Z"
        },
        {
          "id": 3,
          "avatar": "https://example.com/avatar2.jpg",
          "name": "张三",
          "company": "另一家公司",
          "position": "顾问",
          "isDefault": false,
          "createdAt": "2023-06-01T08:00:00Z",
          "updatedAt": "2023-06-10T10:30:00Z"
        }
      ],
      "currentCardId": 1
    }
  }
  ```

#### 2.6 设置默认名片

- **接口**: `POST /api/card/setDefault`
- **描述**: 设置指定ID的名片为用户的默认名片
- **请求参数**:

  ```json
  {
    "cardId": 3 // 要设置为默认的名片ID
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 3,
      "isDefault": true,
      "updatedAt": "2023-07-15T14:30:00Z"
    }
  }
  ```

### 3. 名片夹管理模块

#### 3.1 收藏名片

- **接口**: `POST /collection`
- **描述**: 收藏指定ID的名片到自己的名片夹
- **请求参数**:

  ```json
  {
    "cardId": 2 // 要收藏的名片ID
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1, // 收藏记录ID
      "userId": 1, // 当前用户ID
      "cardId": 2, // 被收藏的名片ID
      "createdAt": "2023-05-15T15:30:00Z"
    }
  }
  ```

#### 3.2 获取名片夹列表

- **接口**: `GET /collection`
- **描述**: 获取当前用户名片夹中收藏的所有名片
- **请求参数**:
  - `page`: 页码，默认1
  - `pageSize`: 每页数量，默认20
  - `keyword`: 搜索关键词，可选，用于按名称、公司或职位搜索
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 30,
      "page": 1,
      "pageSize": 20,
      "list": [
        {
          "id": 2,
          "collectionId": 1,
          "avatar": "https://example.com/avatar2.jpg",
          "name": "李四",
          "company": "某某科技有限公司",
          "position": "产品经理",
          "companyCode": "COMPANY456",
          "collectedAt": "2023-05-15T15:30:00Z"
        },
        // ... 更多名片
      ]
    }
  }
  ```

#### 3.3 删除收藏的名片

- **接口**: `DELETE /collection/:id`
- **描述**: 从名片夹中删除指定ID的收藏记录
- **路径参数**: id - 收藏记录ID
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

#### 3.4 检查名片是否已收藏

- **接口**: `GET /collection/check`
- **描述**: 检查指定ID的名片是否已被当前用户收藏
- **请求参数**: 
  - `cardId`: 要检查的名片ID
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "isCollected": true
    }
  }
  ```

### 4. 企业相关接口

#### 4.1 获取企业信息

- **接口**: `GET /company/info`
- **描述**: 获取企业信息，支持两种方式：
  1. 获取当前用户关联的企业信息（需要登录）
  2. 通过companyCode查询指定企业信息（支持未登录访问）
- **请求参数**: 
  - `companyCode`: 企业编码，可选，指定时可不登录
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "companyCode": "COMPANY123",
      "name": "智链科技有限公司",
      "enterprisePage": "/pages/enterprise/company123/index",
      "productsPage": "/pages/products/company123/index",
      "customPage": "https://example.com/custom-page",
      "enterpriseName": "企业",
      "productsName": "产品中心",
      "customName": "新页面",
      "enterpriseShareImage": "https://cdn.zhilian.com/share/enterprise_company123.jpg",
      "productsShareImage": "https://cdn.zhilian.com/share/products_company123.jpg",
      "customShareImage": "https://cdn.zhilian.com/share/custom_company123.jpg"
    }
  }
  ```

#### 4.2 更新企业页面路径

- **接口**: `PUT /company/pages`
- **描述**: 更新企业页面和产品中心页面的路径（后台维护使用，一般通过数据库直接维护）
- **请求参数**:

  ```json
  {
    "companyCode": "COMPANY123",
    "enterprisePage": "/pages/enterprise/company123/index",
    "productsPage": "/pages/products/company123/index"
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "companyCode": "COMPANY123",
      "enterprisePage": "/pages/enterprise/company123/index",
      "productsPage": "/pages/products/company123/index",
      "updatedAt": "2023-05-20T10:00:00Z"
    }
  }
  ```

**注意**：此接口主要为后台管理预留，实际项目中通常直接通过数据库操作来维护企业页面路径。

#### 4.3 更新企业自定义名称

- **接口**: `PUT /company/custom-names`
- **描述**: 更新企业和产品中心的自定义名称
- **请求参数**:

  ```json
  {
    "enterpriseName": "公司简介", // 自定义企业名称，可选，不传则不更新，最大长度30字符
    "productsName": "产品展示", // 自定义产品中心名称，可选，不传则不更新，最大长度30字符
    "companyCode": "COMPANY123" // 企业编码，可选，不传则使用当前用户关联的企业
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "companyCode": "COMPANY123",
      "enterpriseName": "公司简介",
      "productsName": "产品展示",
      "updatedAt": "2023-06-15T14:30:00Z"
    }
  }
  ```

- **权限**: 需要用户已登录，且用户关联了对应的企业

- **处理说明**: 
  - 服务器会自动截断超过30字符的名称
  - 字段验证在前端和后端都需要进行
  - 更新后会立即应用于所有关联该企业的用户
  - 前端需处理长文本显示的截断和自适应布局

#### 4.4 更新企业分享图片

- **接口**: `PUT /company/share-images`
- **描述**: 更新企业和产品中心的分享图片
- **请求参数**:

  ```json
  {
    "enterpriseShareImage": "https://cdn.zhilian.com/share/enterprise_new.jpg", // 企业分享图URL，可选，不传则不更新
    "productsShareImage": "https://cdn.zhilian.com/share/products_new.jpg", // 产品中心分享图URL，可选，不传则不更新
    "companyCode": "COMPANY123" // 企业编码，必填
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "companyCode": "COMPANY123",
      "enterpriseShareImage": "https://cdn.zhilian.com/share/enterprise_new.jpg",
      "productsShareImage": "https://cdn.zhilian.com/share/products_new.jpg",
      "updatedAt": "2023-06-15T15:00:00Z"
    }
  }
  ```

- **权限**: 需要管理员权限，一般仅供后台管理使用

#### 4.5 搜索企业名称

- **接口**: `GET /company/search`
- **描述**: 根据关键词搜索企业名称，用于自动完成功能
- **请求参数**:
  - `keyword`: 搜索关键词，必填，最少1个字符
- **权限**: 无需登录，支持公开搜索
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "name": "上饶市数字技术应用协会",
        "companyCode": "CO271654575352"
      },
      {
        "name": "上饶市富路工程科技有限公司",
        "companyCode": "CO490390496874"
      },
      {
        "name": "上饶传媒集团",
        "companyCode": "CO956115117114"
      }
    ]
  }
  ```

- **功能特性**:
  - 支持模糊匹配，使用LIKE查询
  - 优先显示以关键词开头的企业
  - 限制返回最多10个结果
  - 按企业名称排序
  - 空关键词返回空数组

### 5. 产品中心相关接口

#### 5.1 获取产品列表

- **接口**: `GET /products`
- **描述**: 获取与用户关联企业的产品列表
- **请求参数**:
  - `companyCode`: 企业编码，可选，不传则使用当前用户关联的企业
  - `page`: 页码，默认1
  - `pageSize`: 每页数量，默认20
- **重要说明**: 此接口也用于检查企业是否有产品，前端通过检查返回的list数组长度来决定是否显示产品中心模块
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 15,
      "page": 1,
      "pageSize": 20,
      "list": [
        {
          "id": 1,
          "title": "智能家居系统",
          "description": "基于物联网的智能家居解决方案",
          "coverImage": "https://cdn.zhilian.com/products/smart_home.jpg",
          "companyCode": "COMPANY123",
          "createdAt": "2023-07-10T08:00:00Z",
          "updatedAt": "2023-07-15T10:30:00Z"
        },
        // ... 更多产品
      ]
    }
  }
  ```

#### 5.2 获取产品详情

- **接口**: `GET /products/:id`
- **描述**: 获取指定ID的产品详情
- **路径参数**: id - 产品ID
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "title": "智能家居系统",
      "description": "基于物联网的智能家居解决方案",
      "coverImage": "https://cdn.zhilian.com/products/smart_home.jpg",
      "content": "产品详细内容...",
      "shareImage": "https://cdn.zhilian.com/share/product_1.jpg",
      "shareTitle": "智链科技 - 智能家居系统",
      "companyCode": "COMPANY123",
      "companyName": "智链科技有限公司",
      "createdAt": "2023-07-10T08:00:00Z",
      "updatedAt": "2023-07-15T10:30:00Z"
    }
  }
  ```

#### 5.3 创建产品（管理员接口）

- **接口**: `POST /products`
- **描述**: 创建新产品
- **请求参数**:

  ```json
  {
    "title": "智能家居系统",
    "description": "基于物联网的智能家居解决方案",
    "coverImage": "https://cdn.zhilian.com/products/smart_home.jpg",
    "content": "产品详细内容...",
    "shareImage": "https://cdn.zhilian.com/share/product_1.jpg",
    "shareTitle": "智链科技 - 智能家居系统",
    "companyCode": "COMPANY123"
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "title": "智能家居系统",
      "description": "基于物联网的智能家居解决方案",
      "coverImage": "https://cdn.zhilian.com/products/smart_home.jpg",
      "content": "产品详细内容...",
      "shareImage": "https://cdn.zhilian.com/share/product_1.jpg",
      "shareTitle": "智链科技 - 智能家居系统",
      "companyCode": "COMPANY123",
      "createdAt": "2023-07-10T08:00:00Z",
      "updatedAt": "2023-07-10T08:00:00Z"
    }
  }
  ```

- **权限**: 需要管理员权限

#### 5.4 更新产品（管理员接口）

- **接口**: `PUT /products/:id`
- **描述**: 更新指定ID的产品信息
- **路径参数**: id - 产品ID
- **请求参数**:

  ```json
  {
    "title": "智能家居解决方案",
    "description": "更新后的产品描述",
    "coverImage": "https://cdn.zhilian.com/products/smart_home_v2.jpg",
    "content": "更新后的产品详细内容...",
    "shareImage": "https://cdn.zhilian.com/share/product_1_v2.jpg",
    "shareTitle": "智链科技 - 智能家居解决方案"
  }
  ```

- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "title": "智能家居解决方案",
      "description": "更新后的产品描述",
      "coverImage": "https://cdn.zhilian.com/products/smart_home_v2.jpg",
      "content": "更新后的产品详细内容...",
      "shareImage": "https://cdn.zhilian.com/share/product_1_v2.jpg",
      "shareTitle": "智链科技 - 智能家居解决方案",
      "companyCode": "COMPANY123",
      "updatedAt": "2023-07-15T10:30:00Z"
    }
  }
  ```

- **权限**: 需要管理员权限

#### 5.5 删除产品（管理员接口）

- **接口**: `DELETE /products/:id`
- **描述**: 删除指定ID的产品
- **路径参数**: id - 产品ID
- **响应示例**:

  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

- **权限**: 需要管理员权限

## 接口权限设计

| 接口                 | 是否需要登录 | 权限要求            |
|----------------------|--------------|---------------------|
| POST /auth/login     | 否           | 无                  |
| GET /auth/user       | 是           | 用户自身信息        |
| GET /card            | 是           | 用户自身名片        |
| POST /card           | 是           | 用户自身名片        |
| GET /card/:id        | 否*          | 支持未登录访问      |
| GET /card/check-status | 是         | 用户自身名片状态    |
| POST /collection     | 是           | 任何登录用户        |
| GET /collection      | 是           | 用户自身收藏        |
| GET /collection/check | 是          | 检查用户收藏状态    |
| DELETE /collection/:id | 是         | 用户自身收藏        |
| GET /company/info    | 否*          | 支持通过companyCode查询 |
| GET /company/search  | 否           | 公开搜索接口        |
| PUT /company/pages   | 是           | 企业管理员          |
| POST /upload/avatar  | 是           | 用户自身头像        |
| GET /upload/token    | 是           | 获取上传凭证        |
| GET /products        | 否*          | 支持通过companyCode查询 |
| GET /products/:id    | 否           | 无                  |
| POST /products       | 是           | 企业管理员          |
| PUT /products/:id    | 是           | 企业管理员          |
| DELETE /products/:id | 是           | 企业管理员          |

*注：标记为"否*"的接口表示在某些情况下不需要登录，但在其他情况下需要登录。

## 错误处理

所有接口在发生错误时，将返回标准错误响应格式，并使用适当的HTTP状态码。项目使用了统一的错误处理中间件，确保错误响应格式一致。

## 安全考虑

1. 所有接口使用HTTPS
2. 敏感数据（如手机号）在数据库中加密存储
3. 使用JWT令牌进行用户身份验证，令牌有效期为7天
4. 接口访问频率限制防止恶意攻击
5. 企业页面路径更新需要企业管理员权限
6. 文件上传大小限制和类型验证

## 文件上传策略

项目使用七牛云作为文件存储服务，支持两种上传方式：
1. 服务器中转上传：客户端上传到服务器，服务器再上传到七牛云
2. 直传方式：客户端获取上传凭证后直接上传到七牛云

## 版本控制

API版本在URL路径中体现，如`/v1/auth/login`，便于后续版本迭代。

## 用户-企业关联逻辑

1. 用户创建/更新名片信息时，系统自动根据公司名称匹配或创建企业记录，并生成company_code
2. 用户表中存储company_code，建立用户与企业的关联
3. 用户点击底部导航的"企业"或"产品中心"时，根据其关联的company_code获取对应的页面路径，跳转到小程序内部对应页面

## 开发与部署

1. 开发环境：Node.js 14+, MySQL 8.0
2. 部署环境：阿里云服务器，使用PM2进行进程管理
3. 数据库连接：使用Sequelize ORM框架
4. 日志记录：使用console.log记录关键操作日志 

## 注意事项

### 1. 名片创建与自定义名称

创建名片时，后端会自动处理以下操作：

1. 根据公司名称自动生成或匹配企业编码（company_code）
2. 自动为新关联的企业设置自定义名称：
   - 企业名称设置为：`[公司名]AI名片`
   - 产品中心名称设置为：`[公司名]产品中心`
3. 自动截断超过长度限制（30字符）的自定义名称
4. 同步更新用户表中的company_code字段

相关代码在`card.controller.js`的`createNewCard`、`createOrUpdateCard`和`updateCardById`方法中实现。

### 2. 用户界面注意事项

前端在显示自定义名称时需注意：

1. 按钮文字区域应自适应内容长度，支持1-2行显示
2. 同一行按钮的高度应根据内容自动调整，保持一致
3. 长文本需自动截断并显示省略号
4. 图标位置不应受文本长度影响，保持在顶部固定位置
5. 所有涉及自定义名称的页面都应处理极端情况（如最大长度30字符） 