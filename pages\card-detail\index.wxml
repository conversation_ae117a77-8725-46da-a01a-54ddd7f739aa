<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
  </view>
  
  <!-- 企业页面web-view -->
  <web-view wx:if="{{showEnterpriseWebView && enterpriseUrl}}" src="{{enterpriseUrl}}">
    <!-- 使用cover-view作为底部导航栏，覆盖在WebView上 -->
    <cover-view class="tab-bar">
      <cover-view class="tab-item {{selected === 'personal' ? 'active' : ''}}" bindtap="goToPersonal">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/personal{{selected === 'personal' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">个人</cover-view>
      </cover-view>
      <cover-view class="tab-item {{selected === 'enterprise' ? 'active' : ''}}" bindtap="goToEnterprise">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/enterprise{{selected === 'enterprise' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">企业</cover-view>
      </cover-view>
      <cover-view class="tab-item {{selected === 'products' ? 'active' : ''}}" bindtap="goToProducts">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/products{{selected === 'products' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">产品中心</cover-view>
      </cover-view>
    </cover-view>
  </web-view>
  
  <!-- 产品中心页面web-view -->
  <web-view wx:if="{{showProductsWebView && productsUrl}}" src="{{productsUrl}}">
    <!-- 使用cover-view作为底部导航栏，覆盖在WebView上 -->
    <cover-view class="tab-bar">
      <cover-view class="tab-item {{selected === 'personal' ? 'active' : ''}}" bindtap="goToPersonal">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/personal{{selected === 'personal' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">个人</cover-view>
      </cover-view>
      <cover-view class="tab-item {{selected === 'enterprise' ? 'active' : ''}}" bindtap="goToEnterprise">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/enterprise{{selected === 'enterprise' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">企业</cover-view>
      </cover-view>
      <cover-view class="tab-item {{selected === 'products' ? 'active' : ''}}" bindtap="goToProducts">
        <cover-image src="https://pic.sdtaa.com/ZhiLian/Picture/Project/tab/products{{selected === 'products' ? '-active' : ''}}.png" class="tab-icon"></cover-image>
        <cover-view class="tab-text">产品中心</cover-view>
      </cover-view>
    </cover-view>
  </web-view>
  
  <!-- 名片详情 -->
  <block wx:if="{{!isLoading && cardInfo && !showEnterpriseWebView && !showProductsWebView}}">
    <!-- 名片展示 -->
    <view class="business-card">
      <view class="card-body">
        <!-- 基本信息 -->
        <view class="card-header">
          <view class="basic-info">
            <view class="name">{{cardInfo.name || '未填写姓名'}}</view>
            <view class="company">{{cardInfo.company || '未填写公司'}}</view>
            <view class="position" wx:if="{{cardInfo.position}}">{{cardInfo.position}}</view>
          </view>
          <image class="avatar" src="{{cardInfo.avatar || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
        
        <!-- 联系方式 -->
        <view class="contact-section">
          <view class="contact-item" bindtap="makePhoneCall" wx:if="{{cardInfo.mobile}}">
            <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/phone.png" mode="aspectFit"></image>
            <view class="contact-value">{{cardInfo.mobile}}</view>
          </view>
          <view class="contact-item" bindtap="copyWechat" wx:if="{{cardInfo.wechat}}">
            <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/wechat.png" mode="aspectFit"></image>
            <view class="contact-value">{{cardInfo.wechat}}</view>
          </view>
          <view class="contact-item" bindtap="sendEmail" wx:if="{{cardInfo.email}}">
            <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/email.png" mode="aspectFit"></image>
            <view class="contact-value">{{cardInfo.email}}</view>
          </view>
          <view class="contact-item" bindtap="copyAddress" wx:if="{{cardInfo.address}}">
            <image class="contact-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/address.png" mode="aspectFit"></image>
            <view class="contact-value">{{cardInfo.address}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮区 -->
    <view class="action-bar">
      <view class="button-wrapper" wx:if="{{!isOwner}}">
        <button
          class="action-button {{isCollected ? 'disabled' : ''}}"
          bindtap="collectCard"
          disabled="{{isCollected || isOwner}}"
        >
          {{isCollected ? '已收藏' : '收下名片'}}
        </button>
      </view>

      <view class="button-wrapper">
        <button
          class="action-button share-button {{isGeneratingShareImage ? 'disabled' : ''}}"
          open-type="{{isGeneratingShareImage ? '' : 'share'}}"
          disabled="{{isGeneratingShareImage}}"
        >
          {{isGeneratingShareImage ? '名片生成中' : '分享名片'}}
        </button>
      </view>

      <!-- AI分身按钮 - 仅在有AI分身URL时显示 -->
      <view class="button-wrapper" wx:if="{{cardInfo.aiAvatarUrl}}">
        <button
          class="action-button ai-avatar-button"
          bindtap="goToAiAvatar"
        >
          AI分身
        </button>
      </view>
    </view>
    
    <!-- 企业和产品中心模块 - 重新组织 -->
    <view class="module-section">
      <!-- 企业模块 - 只有当companyInfo存在且有enterprisePage时才显示 -->
      <view class="module-card" bindtap="viewEnterpriseDetails" wx:if="{{companyInfo && companyInfo.enterprisePage}}">
        <!-- 左侧封面图片 -->
        <view class="module-cover">
          <image class="module-cover-image"
                 src="{{companyInfo.enterpriseShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-enterprise.jpg'}}"
                 mode="aspectFill"></image>
        </view>

        <!-- 右侧内容 -->
        <view class="module-content">
          <view class="module-info">
            <view class="module-title">{{enterpriseName || '企业'}}</view>
            <view class="module-subtitle">{{cardInfo.company || '未填写公司'}}</view>
          </view>
          <view class="module-action">
            <text>查看详情</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 业务简介区 - 放在第二位 -->
    <view class="detail-section" wx:if="{{cardInfo.introduction}}">
      <view class="section-title">业务简介</view>
      <view class="introduction">{{cardInfo.introduction}}</view>
    </view>
    
    <!-- 产品中心模块 - 只有当companyInfo存在且有产品时才显示 -->
    <view class="module-section" wx:if="{{companyInfo && hasProducts}}">
      <view class="module-card" bindtap="viewProductsDetails">
        <!-- 左侧封面图片 -->
        <view class="module-cover">
          <image class="module-cover-image"
                 src="{{companyInfo.productsShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-products.jpg'}}"
                 mode="aspectFill"></image>
        </view>

        <!-- 右侧内容 -->
        <view class="module-content">
          <view class="module-info">
            <view class="module-title">{{productsName || '产品中心'}}</view>
            <view class="module-subtitle">{{cardInfo.company || '未填写公司'}}</view>
          </view>
          <view class="module-action">
            <text>查看详情</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义页面模块 - 只有当companyInfo存在且有自定义页面URL时才显示 -->
    <view class="module-section" wx:if="{{companyInfo && companyInfo.customPage}}">
      <view class="module-card" bindtap="viewCustomPageDetails">
        <!-- 左侧封面图片 -->
        <view class="module-cover">
          <image class="module-cover-image"
                 src="{{companyInfo.customShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/custom-share-default.png'}}"
                 mode="aspectFill"></image>
        </view>

        <!-- 右侧内容 -->
        <view class="module-content">
          <view class="module-info">
            <view class="module-title">{{customName || '新页面'}}</view>
            <view class="module-subtitle">{{cardInfo.company || '未填写公司'}}</view>
          </view>
          <view class="module-action">
            <text>查看详情</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 更多联系方式 - 放在第四位 -->
    <view class="detail-section" wx:if="{{cardInfo.phone || cardInfo.website}}">
      <view class="section-title">更多联系方式</view>
      
      <view class="info-item-horizontal" bindtap="makePhoneCallLandline" wx:if="{{cardInfo.phone}}">
        <view class="info-left">
          <image class="info-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/telephone.png" mode="aspectFit"></image>
          <view class="info-label">座机：</view>
          <view class="info-value">{{cardInfo.phone}}</view>
        </view>
        <view class="info-action">拨打</view>
      </view>
      
      <view class="info-item-horizontal" bindtap="openWebsite" wx:if="{{cardInfo.website}}">
        <view class="info-left">
          <image class="info-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/common/website.png" mode="aspectFit"></image>
          <view class="info-label">网址：</view>
          <view class="info-value">{{cardInfo.website}}</view>
        </view>
        <view class="info-action">复制</view>
      </view>
    </view>
  </block>
  
  <!-- 底部创建/回到我的名片按钮 -->
  <view class="create-card-btn" bindtap="goToMyCard">
    <text>{{hasMyCard ? '返回我的名片' : '创建我的名片'}}</text>
  </view>
  
  <!-- 用于生成分享图的canvas，设置为不可见但保持在DOM中 -->
  <canvas canvas-id="shareCanvas" style="width: 300px; height: 400px; position: fixed; left: -9999px; top: -9999px; visibility: hidden; z-index: -1;"></canvas>
</view> 