/**
 * 企业模型
 */
module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define('Company', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    company_code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '企业唯一标识码'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '企业名称'
    },
    enterprise_page: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '企业页面路径'
    },
    products_page: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '产品中心页面路径'
    },
    // 添加自定义企业名称字段
    enterprise_name: {
      type: DataTypes.STRING(30),
      allowNull: true,
      defaultValue: '企业',
      comment: '自定义企业页面名称'
    },
    // 添加自定义产品中心名称字段
    products_name: {
      type: DataTypes.STRING(30),
      allowNull: true,
      defaultValue: '产品中心',
      comment: '自定义产品中心名称'
    },
    // 添加企业页面分享图片URL字段
    enterprise_share_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '企业页面分享图URL'
    },
    // 添加产品中心页面分享图片URL字段
    products_share_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '产品中心分享图URL'
    },
    // 添加自定义页面相关字段
    custom_page: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '自定义页面路径'
    },
    custom_name: {
      type: DataTypes.STRING(30),
      allowNull: true,
      defaultValue: '新页面',
      comment: '自定义页面名称'
    },
    custom_share_image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '自定义页面分享图URL'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'companies',
    timestamps: true
  });

  // 生成企业编码的静态方法
  Company.generateCompanyCode = async function(companyName) {
    // 首先检查是否已存在该公司名称的企业记录
    const existingCompany = await this.findOne({
      where: { name: companyName }
    });

    if (existingCompany) {
      return existingCompany.company_code;
    }

    // 生成新的公司编码: 'CO' + 时间戳 + 4位随机数
    const timestamp = Date.now().toString().slice(-8);
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    const companyCode = `CO${timestamp}${randomNum}`;

    // 创建新的企业记录
    const newCompany = await this.create({
      company_code: companyCode,
      name: companyName
    });

    return newCompany.company_code;
  };

  return Company;
}; 