# 智链小程序图标清单

项目中的图标分为两部分：本地存储在项目中的图标和存储在云端的图标。

## 本地存储图标

以下图标存储在项目的本地文件夹中：

### 导航栏图标（/images/tab/）
- personal.png - 个人未选中图标
- personal-active.png - 个人选中图标
- enterprise.png - 企业未选中图标
- enterprise-active.png - 企业选中图标
- products.png - 产品中心未选中图标
- products-active.png - 产品中心选中图标
- card-folder.png - 名片夹未选中图标
- card-folder-active.png - 名片夹选中图标

## 云端存储图标

以下图标存储在七牛云存储中，基础URL为：`https://pic.sdtaa.com/ZhiLian/Picture/Project/`

### 1. 默认图片和头像
- default-avatar.png - 默认头像
- default-share.png - 默认分享图片
- share-card.png - 分享卡片图片
- logo.png - 主页logo

### 2. 企业相关图标
- enterprise-default.png - 企业默认图片
- empty-company.png - 空企业图标
- company/default-logo.png - 默认企业logo
- company/address.png - 地址图标
- company/phone.png - 电话图标
- company/email.png - 邮箱图标

### 3. 产品相关图标
- products-default.png - 产品默认图片
- default-products.png - 产品默认图标
- icons/share.png - 产品分享图标
- icons/view.png - 产品查看图标
- icons/empty-products.png - 空产品提示图标

### 5. 功能图标
- camera.png - 相机图标（编辑头像时使用）
- empty-folder.png - 空文件夹图标
- icons/logout.png - 退出登录图标
- icons/customer-service.png - 客服图标（联系我们按钮使用，临时用作AI分身图标）
- icons/ai-avatar.png - AI分身图标（专用于AI分身功能按钮）⚠️ 待创建
- contact.png - 客服二维码（联系我们弹窗使用）

### 6. 联系方式图标（common/）
- common/phone.png - 手机图标
- common/wechat.png - 微信图标
- common/email.png - 邮箱图标
- common/telephone.png - 座机图标
- common/website.png - 网址图标
- common/address.png - 地址图标

### 7. 操作图标（icons/）
- icons/arrow-down.png - 下拉箭头图标
- icons/clear.png - 清除图标
- icons/plus.png - 添加图标
- icons/check.png - 勾选图标
- icons/upload.png - 上传图标
- icons/delete.png - 删除图标
- icons/edit.png - 编辑图标（个人主页-编辑名片按钮图标）
- icons/share.png - 分享图标（分享按钮图标，在多个页面使用）
- icons/collect.png - 收藏图标
- icons/add-card.png - 添加名片图标（个人主页-创建名片图标）
- icons/card-folder.png - 名片夹图标（个人主页-名片夹按钮图标）
- icons/arrow-right.png - 右箭头图标（用于指示可点击进入下一级）
- icons/selected.png - 选中图标（用于切换名片页面标识当前使用的名片）
- icons/settings.png - 设置图标（个人主页-自定义编辑按钮图标）
- icons/view.png - 查看图标（产品中心-查看产品按钮图标）

### 8. CSS图标（无需图片文件）
- 清空按钮图标：使用CSS文字"×"实现（自动完成组件中）
- 加载指示器：使用CSS边框动画实现（自动完成组件中）

## 图标使用位置

### 页面中图标使用
1. **个人页面 (pages/personal/index.wxml)**
   - 添加名片图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/add-card.png`
   - AI分身图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/customer-service.png` (临时使用客服图标)
   - 企业图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png`
   - 产品中心图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/products-default.png`
   - 编辑图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/edit.png`
   - 微信图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/common/wechat.png`
   - 分享图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png`
   - 退出登录图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/logout.png`
   - 客服图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/customer-service.png`
   - 设置图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/settings.png`

2. **名片组件 (components/business-card/index.wxml)**
   - 手机图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/common/phone.png`
   - 微信图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/common/wechat.png`
   - 邮箱图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/common/email.png`
   - 地址图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/common/address.png`

3. **编辑页面 (pages/personal/edit.wxml)**
   - 默认头像: `https://pic.sdtaa.com/ZhiLian/Picture/Project/default-avatar.png`
   - 相机图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/camera.png`

4. **自定义设置页面 (pages/personal/custom-settings.wxml)**
   - 企业分享图: `https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-enterprise.jpg`
   - 产品中心分享图: `https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-products.jpg`

5. **产品中心页面 (pages/products/index.wxml)**
   - 产品默认图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/default-products.png`
   - 分享图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png`
   - 查看图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/view.png`
   - 空产品图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/empty-products.png`

6. **产品详情页面 (pages/products/detail.wxml)**
   - 分享图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png`

7. **联系我们弹窗 (components/contact-popup/index.wxml)**
   - 客服二维码: `https://pic.sdtaa.com/ZhiLian/Picture/Project/contact.png`

8. **名片详情页面 (pages/card-detail/index.wxml)**
   - AI分身图标: `https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/customer-service.png` (临时使用客服图标)

9. **webview页面 (pages/webview/index.wxml)**
   - 用于承载AI分身等外部链接，无需特定图标

10. **自动完成组件 (components/autocomplete-input/index.wxml)**
    - 清空按钮：使用CSS文字"×"（24rpx，#999色）
    - 加载指示器：使用CSS边框动画（20rpx圆形，#3E7FFF色）

### 底部导航栏图标
底部导航栏使用的是本地图标，位于 `/images/tab/` 目录下：
- personal.png 和 personal-active.png - 个人标签页图标
- enterprise.png 和 enterprise-active.png - 企业标签页图标
- products.png 和 products-active.png - 产品中心标签页图标
- card-folder.png 和 card-folder-active.png - 名片夹标签页图标

## 图标尺寸规范
- 导航栏图标: 48rpx × 48rpx
- 操作按钮图标: 40rpx × 40rpx
- 主页logo: 180rpx × 180rpx
- 联系方式图标: 36rpx × 36rpx
- 添加名片图标: 80rpx × 80rpx
- 客服图标: 40rpx × 40rpx
- 客服二维码: 300rpx × 300rpx
- 页面导航栏图标: 32rpx × 32rpx（产品中心、企业页面、产品详情页）
- 自动完成组件图标: 24rpx × 24rpx（清空按钮）、20rpx × 20rpx（加载指示器）

## 图标管理建议

1. **云端图标管理**
   - 所有云端图标应统一存储在七牛云的 `ZhiLian/Picture/Project/` 目录下
   - 按功能分类存储在不同子目录中（common/, icons/, company/等）
   - 图标命名应清晰表明其用途

2. **本地图标管理**
   - 仅保留必要的本地图标（如导航栏图标）
   - 本地图标应按功能组织在对应目录中

3. **图标更新流程**
   - 更新云端图标时，保持URL不变，直接替换文件
   - 更新本地图标时，确保尺寸和格式与原图标一致

4. **图标格式与优化**
   - 所有图标应使用PNG格式，支持透明背景
   - 图标应进行适当压缩，减小小程序包体积
   - 图标设计应遵循统一的视觉风格
   - 避免使用GIF动画，改用CSS动画以减少资源大小和提高性能

## AI分身功能图标说明

### 当前状态
- **临时方案**：使用客服图标 (`icons/customer-service.png`) 作为AI分身按钮图标
- **最终方案**：需要创建专用的AI分身图标 (`icons/ai-avatar.png`)

### 使用位置
1. **个人视角**：个人名片页面的企业按钮左边
2. **他人视角**：分享名片详情页面的分享按钮右边

### 设计要求
- **尺寸**：40rpx × 40rpx
- **背景色**：紫色 (#9C27B0)
- **设计元素**：建议包含AI/智能相关的视觉元素
- **风格一致性**：与现有功能按钮图标保持统一风格

### 待办事项
- [ ] 设计并创建专用的AI分身图标
- [ ] 上传到云端存储：`https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/ai-avatar.png`
- [ ] 更新前端代码中的图标引用路径
- [ ] 测试图标在不同设备上的显示效果