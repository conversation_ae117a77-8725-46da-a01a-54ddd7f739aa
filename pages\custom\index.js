/**
 * 自定义页面入口
 */
const app = getApp();
const { getCompanyInfo } = require('../../utils/company');

Page({
  data: {
    companyCode: '',
    customPage: '',
    customName: '新页面',
    customShareImage: '',
    loading: true,
    error: false
  },

  onLoad(options) {
    console.log('自定义页面加载，参数:', options);
    
    const { companyCode, fromShare } = options;
    
    this.setData({
      companyCode: companyCode || '',
      fromShare: fromShare === 'true'
    });

    this.loadCustomPageInfo();
  },

  /**
   * 加载自定义页面信息
   */
  async loadCustomPageInfo() {
    try {
      this.setData({ loading: true, error: false });

      const companyCode = this.data.companyCode;
      const result = await getCompanyInfo(companyCode);

      console.log('获取企业信息结果:', result);

      // 处理不同的返回数据格式
      let companyData = null;
      if (result && result.customPage !== undefined) {
        // 直接返回的企业信息对象
        companyData = result;
      } else if (result && result.data) {
        // 包装在data字段中的企业信息
        companyData = result.data;
      }

      if (companyData) {
        const { customPage, customName, customShareImage } = companyData;

        console.log('解析的自定义页面数据:', { customPage, customName, customShareImage });

        this.setData({
          customPage: customPage || '',
          customName: customName || '新页面',
          customShareImage: customShareImage || '',
          loading: false
        });

        // 如果有自定义页面URL，跳转到webview
        if (customPage && customPage.trim()) {
          this.navigateToWebview();
        } else {
          console.log('自定义页面URL为空:', customPage);
          // 没有自定义页面URL，显示错误信息
          this.setData({ error: true });
        }
      } else {
        console.log('未获取到有效的企业数据');
        this.setData({ error: true, loading: false });
      }
    } catch (err) {
      console.error('加载自定义页面信息失败:', err);
      this.setData({ error: true, loading: false });
    }
  },

  /**
   * 跳转到webview页面
   */
  navigateToWebview() {
    const { customPage, customName, companyCode, fromShare } = this.data;

    if (!customPage) {
      wx.showToast({
        title: '页面地址不存在',
        icon: 'none'
      });
      return;
    }

    // 构造webview页面参数
    const params = new URLSearchParams({
      url: encodeURIComponent(customPage),
      customName: encodeURIComponent(customName || '自定义页面'),
      companyCode: companyCode || '',
      fromShare: fromShare ? 'true' : 'false'
    });

    wx.redirectTo({
      url: `/pages/custom/webview?${params.toString()}`,
      fail: (err) => {
        console.error('跳转webview失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { customName, customShareImage, companyCode } = this.data;

    return {
      title: customName || '新页面',
      path: `/pages/custom/index?companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { customName, customShareImage, companyCode } = this.data;

    return {
      title: customName || '新页面',
      query: `companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 返回上一页
   */
  goBack() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/personal/index'
      });
    }
  },

  /**
   * 重新加载
   */
  reload() {
    this.loadCustomPageInfo();
  }
});
