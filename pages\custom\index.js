/**
 * 自定义页面入口
 */
const app = getApp();
const { getCompanyInfo } = require('../../utils/company');

Page({
  data: {
    companyCode: '',
    customPage: '',
    customName: '新页面',
    customShareImage: '',
    loading: true,
    error: false,
    showWebview: false,
    // 导航栏相关
    showNavBar: true,
    isNavCollapsed: false,
    cardId: '',
    fromShare: false
  },

  onLoad(options) {
    console.log('自定义页面加载，参数:', options);

    const { companyCode, fromShare, cardId } = options;

    this.setData({
      companyCode: companyCode || '',
      fromShare: fromShare === 'true',
      cardId: cardId || ''
    });

    this.loadCustomPageInfo();
  },

  /**
   * 加载自定义页面信息
   */
  async loadCustomPageInfo() {
    try {
      this.setData({ loading: true, error: false });

      const companyCode = this.data.companyCode;
      const result = await getCompanyInfo(companyCode);

      console.log('获取企业信息结果:', result);

      // 处理不同的返回数据格式
      let companyData = null;
      if (result && result.customPage !== undefined) {
        // 直接返回的企业信息对象
        companyData = result;
      } else if (result && result.data) {
        // 包装在data字段中的企业信息
        companyData = result.data;
      }

      if (companyData) {
        const { customPage, customName, customShareImage } = companyData;

        console.log('解析的自定义页面数据:', { customPage, customName, customShareImage });

        this.setData({
          customPage: customPage || '',
          customName: customName || '新页面',
          customShareImage: customShareImage || '',
          loading: false
        });

        // 如果有自定义页面URL，直接显示webview
        if (customPage && customPage.trim()) {
          this.setData({
            showWebview: true,
            loading: false
          });

          // 设置导航栏标题
          wx.setNavigationBarTitle({
            title: customName || '自定义页面'
          });
        } else {
          console.log('自定义页面URL为空:', customPage);
          // 没有自定义页面URL，显示错误信息
          this.setData({ error: true });
        }
      } else {
        console.log('未获取到有效的企业数据');
        this.setData({ error: true, loading: false });
      }
    } catch (err) {
      console.error('加载自定义页面信息失败:', err);
      this.setData({ error: true, loading: false });
    }
  },



  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { customName, customShareImage, companyCode } = this.data;

    return {
      title: customName || '新页面',
      path: `/pages/custom/index?companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { customName, customShareImage, companyCode } = this.data;

    return {
      title: customName || '新页面',
      query: `companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 返回上一页
   */
  goBack() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/personal/index'
      });
    }
  },

  /**
   * 重新加载
   */
  reload() {
    this.loadCustomPageInfo();
  },

  /**
   * webview加载完成
   */
  onWebviewLoad(e) {
    console.log('webview加载完成:', e);
  },

  /**
   * webview加载失败
   */
  onWebviewError(e) {
    console.error('webview加载失败:', e);
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    });
  },

  /**
   * 显示导航栏
   */
  showNav() {
    console.log('显示导航栏');
    this.setData({
      isNavCollapsed: false
    });
  },

  /**
   * 隐藏导航栏
   */
  hideNav() {
    console.log('隐藏导航栏');
    this.setData({
      isNavCollapsed: true
    });
  },

  /**
   * 返回名片页面
   */
  goBackToCard() {
    if (this.data.cardId) {
      wx.navigateTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}`,
        fail: (err) => {
          console.error("跳转到名片页面失败:", err);
          wx.redirectTo({
            url: `/pages/card-detail/index?id=${this.data.cardId}`
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到原始名片',
        icon: 'none'
      });
    }
  },

  /**
   * 返回个人页面
   */
  goBackToPersonal() {
    wx.switchTab({
      url: '/pages/personal/index'
    });
  },

  /**
   * 跳转到企业页面
   */
  goToEnterprise() {
    const { companyCode, cardId, fromShare } = this.data;
    let url = `/pages/enterprise/index?companyCode=${companyCode}`;

    if (fromShare && cardId) {
      url += `&fromShare=true&cardId=${cardId}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 跳转到产品中心
   */
  goToProducts() {
    const { companyCode, cardId, fromShare } = this.data;
    let url = `/pages/products/index?companyCode=${companyCode}`;

    if (fromShare && cardId) {
      url += `&fromShare=true&cardId=${cardId}`;
    }

    wx.navigateTo({
      url: url
    });
  }
});
