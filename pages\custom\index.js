// pages/custom/index.js
const app = getApp();
const { getCompanyInfo } = require('../../store/company');

Page({
  data: {
    companyCode: '',
    companyInfo: null,
    customPage: '',
    customName: '新页面',
    customShareImage: '',
    loading: true,
    error: false,
    // 导航栏相关
    showNavBar: true,
    navBarButtons: [],
    // 页面参数
    fromShare: false,
    cardId: null
  },

  onLoad(options) {
    console.log('自定义页面加载参数:', options);
    
    // 获取页面参数
    const { companyCode, fromShare, cardId, fromShared } = options;
    
    this.setData({
      companyCode: companyCode || '',
      fromShare: fromShare === 'true' || fromShared === 'true',
      cardId: cardId || null
    });

    // 加载企业信息和自定义页面内容
    this.loadCompanyInfo();
  },

  onShow() {
    // 页面显示时更新导航栏
    this.updateNavBar();
  },

  /**
   * 加载企业信息
   */
  async loadCompanyInfo() {
    try {
      this.setData({ loading: true, error: false });

      let companyInfo;
      if (this.data.companyCode) {
        // 通过companyCode获取企业信息（支持未登录访问）
        companyInfo = await getCompanyInfo(this.data.companyCode);
      } else {
        // 获取当前用户关联的企业信息
        companyInfo = await getCompanyInfo();
      }

      if (companyInfo && companyInfo.data) {
        const data = companyInfo.data;
        this.setData({
          companyInfo: data,
          customPage: data.customPage || '',
          customName: data.customName || '新页面',
          customShareImage: data.customShareImage || '',
          loading: false
        });

        // 更新页面标题
        wx.setNavigationBarTitle({
          title: data.customName || '新页面'
        });

        // 如果有自定义页面URL，加载页面内容
        if (data.customPage) {
          this.loadCustomPageContent(data.customPage);
        } else {
          // 显示默认内容
          this.showDefaultContent();
        }
      } else {
        this.setData({
          loading: false,
          error: true
        });
      }
    } catch (error) {
      console.error('加载企业信息失败:', error);
      this.setData({
        loading: false,
        error: true
      });
    }
  },

  /**
   * 加载自定义页面内容
   */
  loadCustomPageContent(customPageUrl) {
    // 如果是外部URL，跳转到webview页面
    if (customPageUrl.startsWith('http')) {
      wx.redirectTo({
        url: `/pages/webview/index?url=${encodeURIComponent(customPageUrl)}&title=${encodeURIComponent(this.data.customName)}`
      });
    } else {
      // 如果是小程序内部页面，可以在这里处理
      console.log('内部页面路径:', customPageUrl);
      // 这里可以根据实际需求加载内部页面内容
      this.showDefaultContent();
    }
  },

  /**
   * 显示默认内容
   */
  showDefaultContent() {
    // 显示默认的自定义页面内容
    console.log('显示默认自定义页面内容');
  },

  /**
   * 更新导航栏
   */
  updateNavBar() {
    const buttons = [];
    
    if (this.data.fromShare && this.data.cardId) {
      // 分享视角：返回名片按钮
      buttons.push({
        text: '返回名片',
        icon: 'card',
        action: 'backToCard'
      });
    } else if (this.data.fromShare && this.data.companyCode) {
      // 分享视角：返回主页按钮
      buttons.push({
        text: '返回主页',
        icon: 'home',
        action: 'backToEnterprise'
      });
    } else {
      // 个人视角：个人页面按钮
      buttons.push({
        text: '个人页面',
        icon: 'personal',
        action: 'goToPersonal'
      });
    }

    // 企业页面按钮
    if (this.data.companyInfo && this.data.companyInfo.enterprisePage) {
      buttons.push({
        text: this.data.companyInfo.enterpriseName || '企业',
        icon: 'enterprise',
        action: 'goToEnterprise'
      });
    }

    // 产品中心按钮
    if (this.data.companyInfo && this.data.companyInfo.productsPage) {
      buttons.push({
        text: this.data.companyInfo.productsName || '产品中心',
        icon: 'products',
        action: 'goToProducts'
      });
    }

    // 分享按钮
    buttons.push({
      text: '分享',
      icon: 'share',
      action: 'share'
    });

    this.setData({
      navBarButtons: buttons
    });
  },

  /**
   * 导航栏按钮点击事件
   */
  onNavButtonTap(e) {
    const { action } = e.currentTarget.dataset;
    
    switch (action) {
      case 'backToCard':
        this.backToCard();
        break;
      case 'backToEnterprise':
        this.backToEnterprise();
        break;
      case 'goToPersonal':
        this.goToPersonal();
        break;
      case 'goToEnterprise':
        this.goToEnterprise();
        break;
      case 'goToProducts':
        this.goToProducts();
        break;
      case 'share':
        this.shareCustomPage();
        break;
    }
  },

  /**
   * 返回名片
   */
  backToCard() {
    if (this.data.cardId) {
      wx.redirectTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}&fromShare=true`
      });
    }
  },

  /**
   * 返回企业页面
   */
  backToEnterprise() {
    if (this.data.companyCode) {
      wx.redirectTo({
        url: `/pages/enterprise/index?companyCode=${this.data.companyCode}&fromShare=true`
      });
    }
  },

  /**
   * 跳转到个人页面
   */
  goToPersonal() {
    wx.switchTab({
      url: '/pages/personal/index'
    });
  },

  /**
   * 跳转到企业页面
   */
  goToEnterprise() {
    const url = this.data.fromShare 
      ? `/pages/enterprise/index?companyCode=${this.data.companyCode}&fromShare=true&cardId=${this.data.cardId}`
      : '/pages/enterprise/index';
    
    wx.redirectTo({ url });
  },

  /**
   * 跳转到产品中心
   */
  goToProducts() {
    const url = this.data.fromShare 
      ? `/pages/products/index?companyCode=${this.data.companyCode}&fromShare=true&cardId=${this.data.cardId}`
      : '/pages/products/index';
    
    wx.redirectTo({ url });
  },

  /**
   * 分享自定义页面
   */
  shareCustomPage() {
    const { companyInfo, companyCode } = this.data;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    const { companyInfo, companyCode } = this.data;
    
    return {
      title: `${companyInfo?.name || '企业'} - ${companyInfo?.customName || '新页面'}`,
      path: `/pages/custom/index?companyCode=${companyCode}&fromShare=true`,
      imageUrl: companyInfo?.customShareImage || ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { companyInfo, companyCode } = this.data;
    
    return {
      title: `${companyInfo?.name || '企业'} - ${companyInfo?.customName || '新页面'}`,
      query: `companyCode=${companyCode}&fromShare=true`,
      imageUrl: companyInfo?.customShareImage || ''
    };
  },

  /**
   * 重新加载
   */
  onRetry() {
    this.loadCompanyInfo();
  }
});
