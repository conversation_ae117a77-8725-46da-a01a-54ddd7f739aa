/**
 * 自定义页面入口
 */
const app = getApp();
const { getCompanyInfo } = require('../../utils/company');

Page({
  data: {
    companyCode: '',
    customPage: '',
    customName: '新页面',
    customShareImage: '',
    loading: true,
    error: false
  },

  onLoad(options) {
    console.log('自定义页面加载，参数:', options);
    
    const { companyCode, fromShare } = options;
    
    this.setData({
      companyCode: companyCode || '',
      fromShare: fromShare === 'true'
    });

    this.loadCustomPageInfo();
  },

  /**
   * 加载自定义页面信息
   */
  async loadCustomPageInfo() {
    try {
      this.setData({ loading: true, error: false });
      
      const companyCode = this.data.companyCode;
      const result = await getCompanyInfo(companyCode);
      
      console.log('获取企业信息结果:', result);
      
      if (result && result.data) {
        const { customPage, customName, customShareImage } = result.data;
        
        this.setData({
          customPage: customPage || '',
          customName: customName || '新页面',
          customShareImage: customShareImage || '',
          loading: false
        });

        // 如果有自定义页面URL，跳转到webview
        if (customPage) {
          this.navigateToWebview();
        } else {
          // 没有自定义页面URL，显示错误信息
          this.setData({ error: true });
        }
      } else {
        this.setData({ error: true, loading: false });
      }
    } catch (err) {
      console.error('加载自定义页面信息失败:', err);
      this.setData({ error: true, loading: false });
    }
  },

  /**
   * 跳转到webview页面
   */
  navigateToWebview() {
    const { customPage, companyCode, fromShare } = this.data;
    
    if (!customPage) {
      wx.showToast({
        title: '页面地址不存在',
        icon: 'none'
      });
      return;
    }

    // 构造webview页面参数
    const params = new URLSearchParams({
      url: encodeURIComponent(customPage),
      companyCode: companyCode || '',
      fromShare: fromShare ? 'true' : 'false',
      pageType: 'custom'
    });

    wx.redirectTo({
      url: `/pages/webview/index?${params.toString()}`,
      fail: (err) => {
        console.error('跳转webview失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { customName, customShareImage, companyCode } = this.data;
    
    return {
      title: customName || '新页面',
      path: `/pages/custom/index?companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { customName, customShareImage, companyCode } = this.data;
    
    return {
      title: customName || '新页面',
      query: `companyCode=${companyCode}&fromShare=true`,
      imageUrl: customShareImage || ''
    };
  },

  /**
   * 返回上一页
   */
  goBack() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/personal/index'
      });
    }
  },

  /**
   * 重新加载
   */
  reload() {
    this.loadCustomPageInfo();
  }
});
