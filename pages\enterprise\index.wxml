<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
  </view>
  
  <!-- 外部链接页面 - 添加了底部安全区域 -->
  <web-view wx:if="{{!isLoading && externalUrl}}" src="{{externalUrl}}" class="full-webview">
    <!-- 右侧导航栏 - 只在showNavBar为true时显示 -->
    <cover-view class="nav-bar {{isNavCollapsed ? 'collapsed' : ''}}" wx:if="{{showNavBar}}">
      <!-- 显示导航按钮 - 当导航栏收起时显示 -->
      <cover-view class="toggle-btn" bindtap="showNav" hidden="{{!isNavCollapsed}}">
        <cover-view class="toggle-text vertical">
          <cover-view class="toggle-char">显</cover-view>
          <cover-view class="toggle-char">示</cover-view>
          <cover-view class="toggle-char">导</cover-view>
          <cover-view class="toggle-char">航</cover-view>
        </cover-view>
      </cover-view>

      <!-- 隐藏导航按钮 - 当导航栏展开时显示 -->
      <cover-view class="toggle-btn" bindtap="hideNav" hidden="{{isNavCollapsed}}">
        <cover-view class="toggle-text vertical">
          <cover-view class="toggle-char">隐</cover-view>
          <cover-view class="toggle-char">藏</cover-view>
          <cover-view class="toggle-char">导</cover-view>
          <cover-view class="toggle-char">航</cover-view>
        </cover-view>
      </cover-view>

      <!-- 导航按钮区域 -->
      <cover-view class="nav-buttons" hidden="{{isNavCollapsed}}">
        <!-- 返回名片按钮，仅当从分享进入且有cardId时显示 -->
        <cover-view class="nav-btn" wx:if="{{fromShare && cardId}}" bindtap="goBackToCard">
          <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></cover-image>
          <cover-view class="btn-text">返回名片</cover-view>
        </cover-view>

        <!-- 返回个人页面按钮，当从个人视角进入时显示 -->
        <cover-view class="nav-btn" wx:if="{{!fromShare}}" bindtap="goBackToPersonal">
          <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></cover-image>
          <cover-view class="btn-text">个人页面</cover-view>
        </cover-view>

        <!-- 产品中心按钮 -->
        <cover-view class="nav-btn" wx:if="{{companyInfo && companyInfo.companyCode}}" bindtap="goToProducts">
          <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/default-products.png"></cover-image>
          <cover-view class="btn-text">产品中心</cover-view>
        </cover-view>

        <!-- 自定义页面按钮 -->
        <cover-view class="nav-btn" wx:if="{{companyInfo && companyInfo.customPage}}" bindtap="goToCustomPage">
          <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/default-products.png"></cover-image>
          <cover-view class="btn-text">{{customName || '新页面'}}</cover-view>
        </cover-view>

        <!-- 分享按钮 - 使用button的open-type="share"直接调起分享功能 -->
        <button class="nav-btn-wrapper share-btn" open-type="share">
          <cover-view class="nav-btn">
            <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png"></cover-image>
            <cover-view class="btn-text">分享</cover-view>
          </cover-view>
        </button>
      </cover-view>
    </cover-view>
  </web-view>
  
  <!-- 默认企业页 -->
  <view class="default-container" wx:if="{{!isLoading && !hasCompanyCode}}">
    <image class="default-image" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png" mode="aspectFit"></image>
    <view class="default-title">暂无关联企业</view>
    <view class="default-subtitle">请先在个人名片中填写您的公司信息</view>
    <button class="action-btn" bindtap="goToEditCard">填写名片信息</button>
  </view>
  
  <!-- 默认显示（有企业但无外部链接） -->
  <view class="default-container" wx:if="{{!isLoading && hasCompanyCode && !externalUrl}}">
    <image class="default-image" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png" mode="aspectFit"></image>
    <view class="default-title">企业页面尚未设置</view>
    <view class="default-subtitle">您的企业还未创建企业名片哦，请联系我们为您打造个性化的专属企业AI名片~</view>
    <image class="contact-qrcode" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/contact.png" mode="aspectFit" show-menu-by-longpress="true"></image>
    <view class="qrcode-tip">长按二维码添加客服咨询</view>
  </view>
</view> 