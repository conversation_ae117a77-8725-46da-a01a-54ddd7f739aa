<view class="container">
  <view class="header">
    <view class="title">自定义设置</view>
    <view class="subtitle">设置将替换应用中所有"企业"和"产品中心"的显示名称</view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
  </view>
  
  <!-- 未关联企业提示 -->
  <view class="no-company" wx:if="{{!isLoading && !hasCompany}}">
    <image class="no-company-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/info.png" mode="aspectFit"></image>
    <view class="no-company-text">您尚未关联企业，无法进行自定义设置</view>
  </view>
  
  <!-- 设置表单 -->
  <block wx:if="{{!isLoading && hasCompany}}">
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">企业名称</view>
        <input class="form-input" value="{{enterpriseName}}" bindinput="onEnterpriseNameInput" maxlength="30" placeholder="请输入企业名称（最多30字）" />
        <view class="char-count">{{enterpriseName.length}}/30</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">产品中心名称</view>
        <input class="form-input" value="{{productsName}}" bindinput="onProductsNameInput" maxlength="30" placeholder="请输入产品中心名称（最多30字）" />
        <view class="char-count">{{productsName.length}}/30</view>
      </view>

      <view class="form-item">
        <view class="form-label">自定义页面名称</view>
        <input class="form-input" value="{{customName}}" bindinput="onCustomNameInput" maxlength="30" placeholder="请输入自定义页面名称（最多30字）" />
        <view class="char-count">{{customName.length}}/30</view>
      </view>
    </view>
    
    <view class="tips">
      <view class="tip-item">
        <view class="tip-text">自定义名称将替换应用中所有"企业"和"产品中心"的文字</view>
      </view>
      <view class="tip-item">
        <view class="tip-text">包括名片详情页、分享页面和底部导航栏等位置</view>
      </view>
      <view class="tip-item">
        <view class="tip-text">修改后，所有关联该企业的名片将使用新的名称</view>
      </view>
    </view>
    
    <!-- 分享图预览 -->
    <view class="form-container share-images-container">
      <view class="form-title">分享图预览</view>
      <view class="form-subtitle">以下图片将在微信分享时显示</view>
      
      <view class="share-images-grid">
        <view class="share-image-item">
          <view class="share-image-label">{{enterpriseName}}分享图</view>
          <view class="share-image-wrapper" bindtap="previewEnterpriseShareImage">
            <image class="share-image" src="{{enterpriseShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-enterprise.jpg'}}" mode="aspectFit"></image>
            <view class="share-image-mask">
              <view class="preview-text">查看大图</view>
            </view>
          </view>
        </view>
        
        <view class="share-image-item">
          <view class="share-image-label">{{productsName}}分享图</view>
          <view class="share-image-wrapper" bindtap="previewProductsShareImage">
            <image class="share-image" src="{{productsShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-products.jpg'}}" mode="aspectFit"></image>
            <view class="share-image-mask">
              <view class="preview-text">查看大图</view>
            </view>
          </view>
        </view>

        <view class="share-image-item">
          <view class="share-image-label">{{customName}}分享图</view>
          <view class="share-image-wrapper" bindtap="previewCustomShareImage">
            <image class="share-image" src="{{customShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-custom.jpg'}}" mode="aspectFit"></image>
            <view class="share-image-mask">
              <view class="preview-text">查看大图</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="tip-item share-images-note">
        <view class="tip-text">如需修改分享图，请联系客服</view>
      </view>
    </view>
    
    <button class="save-button" bindtap="saveSettings" disabled="{{isSaving}}">
      {{isSaving ? '保存中...' : '保存设置'}}
    </button>
  </block>
</view> 