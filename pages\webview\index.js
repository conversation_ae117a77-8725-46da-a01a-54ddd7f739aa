Page({
  data: {
    url: '',
    pageType: '',
    pageTitle: ''
  },

  onLoad(options) {
    console.log('webview页面加载，参数:', options);

    const { url, pageType, fromShare, companyCode } = options;

    if (url) {
      const decodedUrl = decodeURIComponent(url);
      console.log('解码后的URL:', decodedUrl);

      // 根据页面类型设置标题
      let pageTitle = 'AI分身';
      if (pageType === 'custom') {
        pageTitle = '自定义页面';
      }

      this.setData({
        url: decodedUrl,
        pageType: pageType || '',
        pageTitle: pageTitle,
        fromShare: fromShare === 'true',
        companyCode: companyCode || ''
      });
    } else {
      wx.showToast({
        title: '链接地址无效',
        icon: 'none'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  onShow() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.pageTitle || 'AI分身'
    });
  },

  // webview加载完成
  onWebviewLoad(e) {
    console.log('webview加载完成:', e);
  },

  // webview加载失败
  onWebviewError(e) {
    console.error('webview加载失败:', e);
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    });
  }
});
