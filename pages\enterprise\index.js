const app = getApp()

Page({
  data: {
    hasCompanyCode: false,
    companyInfo: null,
    isLoading: true,
    externalUrl: '', // 存储外部URL
    cardId: null,     // 存储来自分享链接的卡片ID
    fromCardDetail: false, // 标记是否来自名片详情页
    fromSharedCard: false,  // 标记是否来自分享的名片
    fromShare: false, // 标记是否来自分享
    fromPersonal: false, // 标记是否来自个人页面
    showNavBar: false, // 是否显示导航栏
    isNavCollapsed: false, // 导航栏是否收起
    customName: '新页面' // 自定义页面名称
  },

  onLoad(options) {
    console.log("企业页面onLoad, 选项:", options);

    // 现在所有情况都显示导航栏
    this.setData({
      showNavBar: true
    });

    // 检查是否是从分享进入
    if (options && options.fromShare === 'true') {
      console.log("从分享进入企业页面");
      this.setData({
        fromShare: true
      });

      // 如果有companyCode，直接加载企业信息
      if (options.companyCode) {
        this.loadCompanyInfoByCode(options.companyCode, options.cardId);
        return;
      }
    }

    // 检查是否从个人页面进入
    if (options && options.fromPersonal === 'true') {
      console.log("从个人页面进入企业页面，显示导航栏");
      if (options.cardId) {
        this.setData({
          cardId: options.cardId,
          fromPersonal: true
        });
        console.log("设置来自个人页面的cardId:", options.cardId);
      }
    }

    // 如果是从名片详情页跳转过来，获取card_id
    if (options && options.cardId) {
      this.setData({
        cardId: options.cardId,
        fromCardDetail: options.fromCardDetail === 'true'
      })

      console.log("从名片详情页跳转，cardId:", options.cardId);

      // 如果是从名片详情页跳转过来，先尝试获取该名片的企业信息
      if (this.data.fromCardDetail) {
        this.loadCardCompanyInfo(options.cardId);
        return;
      }
    }
    
    // 检查是否是从分享的名片页面通过switchTab跳转过来的
    const fromSharedCard = wx.getStorageSync('from_shared_card');
    if (fromSharedCard) {
      console.log("从分享的名片页面通过switchTab跳转过来");
      
      // 获取存储的企业信息
      const sharedCompanyInfo = wx.getStorageSync('shared_card_company_info');
      const sharedCardId = wx.getStorageSync('shared_card_id');
      
      if (sharedCompanyInfo) {
        console.log("获取到分享名片的企业信息:", sharedCompanyInfo);
        
        this.setData({
          hasCompanyCode: true,
          companyInfo: sharedCompanyInfo,
          isLoading: false,
          fromSharedCard: true,
          cardId: sharedCardId
        });
        
        // 如果有企业页面链接，直接在页面中显示外部链接
        if (sharedCompanyInfo.enterprisePage) {
          // 检查enterprisePage是否为http或https开头的外部链接
          if (sharedCompanyInfo.enterprisePage.startsWith('http://') || 
              sharedCompanyInfo.enterprisePage.startsWith('https://')) {
            console.log("设置外部URL:", sharedCompanyInfo.enterprisePage);
            this.setData({
              externalUrl: sharedCompanyInfo.enterprisePage
            });
          } else {
            console.log("企业页面路径不是外部链接:", sharedCompanyInfo.enterprisePage);
          }
        } else {
          console.log("没有企业页面路径");
        }
        
        // 清除存储，避免影响下次正常访问
        wx.removeStorageSync('from_shared_card');
        return;
      } else {
        console.log("没有找到分享名片的企业信息");
        // 清除标记
        wx.removeStorageSync('from_shared_card');
      }
    }
    
    // 正常流程，检查当前用户的企业编码
    this.checkCompanyCode();
  },

  onShow() {
    // 检查是否是从分享的名片页面通过switchTab跳转过来的
    const fromSharedCard = wx.getStorageSync('from_shared_card');
    if (fromSharedCard) {
      console.log("onShow - 从分享的名片页面通过switchTab跳转过来");
      
      // 获取存储的企业信息
      const sharedCompanyInfo = wx.getStorageSync('shared_card_company_info');
      const sharedCardId = wx.getStorageSync('shared_card_id');
      
      if (sharedCompanyInfo) {
        console.log("onShow - 获取到分享名片的企业信息:", sharedCompanyInfo);
        
        this.setData({
          hasCompanyCode: true,
          companyInfo: sharedCompanyInfo,
          isLoading: false,
          fromSharedCard: true,
          cardId: sharedCardId
        });
        
        // 如果有企业页面链接，直接在页面中显示外部链接
        if (sharedCompanyInfo.enterprisePage) {
          // 检查enterprisePage是否为http或https开头的外部链接
          if (sharedCompanyInfo.enterprisePage.startsWith('http://') || 
              sharedCompanyInfo.enterprisePage.startsWith('https://')) {
            console.log("onShow - 设置外部URL:", sharedCompanyInfo.enterprisePage);
            this.setData({
              externalUrl: sharedCompanyInfo.enterprisePage
            });
          } else {
            console.log("onShow - 企业页面路径不是外部链接:", sharedCompanyInfo.enterprisePage);
          }
        } else {
          console.log("onShow - 没有企业页面路径");
        }
        
        // 设置页面标题为自定义企业名称
        if (sharedCompanyInfo.enterpriseName) {
          wx.setNavigationBarTitle({
            title: sharedCompanyInfo.enterpriseName
          });
        }
        
        // 清除存储，避免影响下次正常访问
        wx.removeStorageSync('from_shared_card');
        return;
      } else {
        console.log("onShow - 没有找到分享名片的企业信息");
        // 清除标记
        wx.removeStorageSync('from_shared_card');
      }
    }
    
    // 如果不是从名片详情页跳转、不是从分享进入、也没有处理过分享卡片的信息，才在页面显示时再次检查
    if (!this.data.fromCardDetail && !this.data.fromSharedCard && !this.data.fromShare) {
      console.log("页面显示，检查企业编码");
      this.checkCompanyCode();
    } else {
      console.log("跳过企业编码检查，原因：", {
        fromCardDetail: this.data.fromCardDetail,
        fromSharedCard: this.data.fromSharedCard,
        fromShare: this.data.fromShare
      });
    }
    
    // 显示自定义tabBar，延迟执行确保页面已完全加载
    setTimeout(() => {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1 // 选中企业页面
        });
        // 更新 tabBar 显示状态
        this.getTabBar().checkLoginPage();
      }
    }, 100);
    
    // 设置页面标题为自定义企业名称
    if (this.data.companyInfo && this.data.companyInfo.enterpriseName) {
      wx.setNavigationBarTitle({
        title: this.data.companyInfo.enterpriseName
      });
    }
  },
  
  // 根据名片ID加载企业信息
  loadCardCompanyInfo(cardId) {
    console.log("开始根据名片ID加载企业信息:", cardId);
    
    this.setData({
      isLoading: true
    });
    
    const card = require('../../utils/card.js');
    card.getCardById(cardId).then(res => {
      console.log("获取名片详情返回:", res);
      
      if (res.code === 0 && res.data) {
        // 处理字段名不一致的问题
        const cardData = res.data;
        // 检查是否有company_code字段但没有companyCode字段的情况
        if (cardData.company_code !== undefined && cardData.companyCode === undefined) {
          cardData.companyCode = cardData.company_code;
          console.log("从company_code转换为companyCode:", cardData.companyCode);
        }
        
        if (cardData.companyCode) {
          console.log("获取到名片的企业编码:", cardData.companyCode);
          
          // 根据名片的企业编码获取企业信息
          const company = require('../../utils/company.js');
          company.getCompanyInfo(cardData.companyCode).then(companyRes => {
            console.log("根据名片企业编码获取企业信息结果:", companyRes);
            
            if (companyRes && companyRes.companyCode) {
              this.setData({
                hasCompanyCode: true,
                companyInfo: companyRes,
                isLoading: false,
                customName: companyRes.customName || '新页面'
              });
              
              // 如果有企业页面链接，直接在页面中显示外部链接
              if (companyRes.enterprisePage) {
                // 检查enterprisePage是否为http或https开头的外部链接
                if (companyRes.enterprisePage.startsWith('http://') || 
                    companyRes.enterprisePage.startsWith('https://')) {
                  console.log("设置外部URL:", companyRes.enterprisePage);
                  this.setData({
                    externalUrl: companyRes.enterprisePage
                  });
                } else {
                  console.log("企业页面路径不是外部链接:", companyRes.enterprisePage);
                }
              } else {
                console.log("没有企业页面路径");
              }
            } else {
              console.log("未获取到有效的企业信息");
              this.setData({
                hasCompanyCode: false,
                isLoading: false
              });
            }
          }).catch(err => {
            console.error("获取企业信息失败:", err);
            this.setData({
              hasCompanyCode: false,
              isLoading: false
            });
          });
        } else {
          console.log("名片没有关联企业编码");
          this.setData({
            hasCompanyCode: false,
            isLoading: false
          });
        }
      } else {
        console.log("获取名片信息失败:", res);
        this.setData({
          hasCompanyCode: false,
          isLoading: false
        });
      }
    }).catch(err => {
      console.error("获取名片失败:", err);
      this.setData({
        hasCompanyCode: false,
        isLoading: false
      });
    });
  },

  // 根据企业代码加载企业信息（用于分享进入）
  loadCompanyInfoByCode(companyCode, cardId) {
    console.log("根据企业代码加载企业信息:", companyCode, "cardId:", cardId);

    if (cardId) {
      this.setData({
        cardId: cardId
      });
    }

    const company = require('../../utils/company.js');
    company.getCompanyInfo(companyCode).then(companyRes => {
      console.log("根据企业代码获取企业信息结果:", companyRes);

      if (companyRes && companyRes.companyCode) {
        this.setData({
          hasCompanyCode: true,
          companyInfo: companyRes,
          isLoading: false,
          customName: companyRes.customName || '新页面'
        });

        // 如果有企业页面链接，直接在页面中显示外部链接
        if (companyRes.enterprisePage) {
          // 检查enterprisePage是否为http或https开头的外部链接
          if (companyRes.enterprisePage.startsWith('http://') ||
              companyRes.enterprisePage.startsWith('https://')) {
            console.log("设置外部URL:", companyRes.enterprisePage);
            this.setData({
              externalUrl: companyRes.enterprisePage
            });
          } else {
            console.log("企业页面路径不是外部链接:", companyRes.enterprisePage);
          }
        } else {
          console.log("没有企业页面路径");
        }

        // 设置页面标题为自定义企业名称
        if (companyRes.enterpriseName) {
          wx.setNavigationBarTitle({
            title: companyRes.enterpriseName
          });
        }
      } else {
        console.log("未获取到有效的企业信息");
        this.setData({
          hasCompanyCode: false,
          isLoading: false
        });
      }
    }).catch(err => {
      console.error("获取企业信息失败:", err);
      this.setData({
        hasCompanyCode: false,
        isLoading: false
      });
    });
  },

  // 检查用户关联的企业编码
  checkCompanyCode() {
    if (!app.globalData.userInfo) {
      this.setData({
        hasCompanyCode: false,
        isLoading: false
      })
      return
    }

    // 已有企业信息，不再加载
    if (this.data.companyInfo && !this.data.isLoading && this.data.externalUrl) return

    this.setData({
      isLoading: true
    })

    const company = require('../../utils/company.js')
    company.getCompanyInfo().then(res => {
      console.log("企业页面获取公司信息结果:", res)
      
      // 直接检查返回的是否为有效对象，不检查code和data字段
      if (res && typeof res === 'object' && res.companyCode) {
        // 因为直接返回的是企业信息对象，而不是包含code和data的标准结构
        const companyData = res;
        
        // 记录企业信息
        this.setData({
          hasCompanyCode: true,
          companyInfo: companyData,
          isLoading: false,
          customName: companyData.customName || '新页面'
        })
        
        console.log("企业页面路径:", companyData.enterprisePage)

        // 如果有企业页面链接，直接在页面中显示外部链接
        if (companyData.enterprisePage) {
          // 检查enterprisePage是否为http或https开头的外部链接
          if (companyData.enterprisePage.startsWith('http://') || companyData.enterprisePage.startsWith('https://')) {
            console.log("设置外部URL:", companyData.enterprisePage)
            this.setData({
              externalUrl: companyData.enterprisePage
            })
          } else {
            console.log("企业页面路径不是外部链接:", companyData.enterprisePage)
          }
        } else {
          console.log("没有企业页面路径")
        }
      } else {
        console.log("获取企业信息失败或数据格式错误", res)
        this.setData({
          hasCompanyCode: false,
          isLoading: false
        })
      }
    }).catch(err => {
      console.error('获取企业信息失败', err)
      this.setData({
        hasCompanyCode: false,
        isLoading: false
      })
    })
  },

  // 跳转到个人页面编辑名片
  goToEditCard() {
    wx.switchTab({
      url: '/pages/personal/index',
      success: () => {
        // 延迟执行，确保先完成tab切换
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/personal/edit',
          })
        }, 100)
      }
    })
  },
  
  // 显示导航栏
  showNav() {
    console.log('显示导航栏');
    this.setData({
      isNavCollapsed: false
    });
  },

  // 隐藏导航栏
  hideNav() {
    console.log('隐藏导航栏');
    this.setData({
      isNavCollapsed: true
    });
  },

  // 返回名片页面
  goBackToCard() {
    if (this.data.cardId) {
      wx.navigateTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}`,
        fail: (err) => {
          console.error("跳转到名片页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: `/pages/card-detail/index?id=${this.data.cardId}`
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到原始名片',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 返回个人页面
  goBackToPersonal() {
    wx.switchTab({
      url: '/pages/personal/index'
    });
  },

  // 跳转到产品中心
  goToProducts() {
    if (this.data.companyInfo && this.data.companyInfo.companyCode) {
      let url = `/pages/products/index?companyCode=${this.data.companyInfo.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到产品中心失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 跳转到自定义页面
  goToCustomPage() {
    if (this.data.companyInfo && this.data.companyInfo.companyCode) {
      let url = `/pages/custom/index?companyCode=${this.data.companyInfo.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到自定义页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 添加分享功能
  onShareAppMessage() {
    // 使用统一分享模块
    const share = require('../../utils/share');

    const companyInfo = this.data.companyInfo;
    let cardId = this.data.cardId;

    // 如果没有cardId，尝试从全局数据中获取当前用户的名片ID
    if (!cardId && app.globalData.cardInfo && app.globalData.cardInfo.id) {
      cardId = app.globalData.cardInfo.id;
      console.log("从全局数据获取cardId:", cardId);
    }

    console.log("企业页面分享信息：", {
      cardId,
      companyInfo,
      fromPersonal: this.data.fromPersonal
    });

    if (!companyInfo || !companyInfo.companyCode) {
      console.error('分享企业主页失败：缺少有效的企业信息');
      return share.getDefaultShare();
    }

    // 准备分享数据
    const shareData = {
      cardInfo: cardId ? { id: cardId } : null,
      companyInfo: companyInfo,
      enterpriseName: companyInfo.enterpriseName
    };

    console.log('最终企业分享数据:', shareData);

    // 使用统一分享模块分享企业
    return share.handleShare({
      type: 'enterprise',
      data: shareData
    });
  }
}) 