/* 自定义页面样式 */
.custom-page-container {
  min-height: 100vh;
  background-color: #f8faff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

/* webview容器样式 */
.webview-container {
  width: 100%;
  height: 100vh;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #3e7fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 30rpx;
}

.retry-btn, .back-btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.retry-btn {
  background-color: #3e7fff;
  color: white;
}

.back-btn {
  background-color: #f5f5f5;
  color: #333;
}

.retry-btn:active {
  background-color: #2c5ce6;
}

.back-btn:active {
  background-color: #e5e5e5;
}

/* 内容状态样式 */
.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.page-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  bottom: 80rpx;
  right: 0;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  z-index: 99999;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 12rpx 0 0 12rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 230, 230, 0.8);
}

/* 导航栏收起状态 */
.nav-bar.collapsed {
  width: auto;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.nav-bar.collapsed .nav-buttons {
  display: none;
}

.nav-bar.collapsed .toggle-btn {
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
}

/* 导航按钮区域 */
.nav-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6rpx 18rpx;
}

/* 单个导航按钮 */
.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 16rpx;
}

/* 按钮图标 */
.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 3rpx;
}

/* 按钮文字 */
.btn-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 切换按钮 */
.toggle-btn {
  padding: 9rpx 7rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx 0 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -4rpx 0 10rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 230, 230, 0.8);
  border-right: none;
  z-index: 100000;
  min-width: 38rpx;
  position: relative;
}

/* 切换按钮文字 */
.toggle-text {
  font-size: 20rpx;
  color: #3E7FFF;
  text-align: center;
}

/* 垂直布局的toggle-text */
.toggle-text.vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  padding: 5rpx 0;
  min-height: 90rpx;
}

/* 按钮中的字 */
.toggle-char {
  font-size: 18rpx;
  color: #3E7FFF;
  text-align: center;
  line-height: 1.1;
  padding: 1rpx 0;
  display: block;
}

/* 导航按钮区域中的分享按钮 */
.nav-btn-wrapper.share-btn {
  padding: 0;
  margin: 0 16rpx;
  background: none;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: normal;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  font-size: inherit;
  color: inherit;
  width: auto;
}

.nav-btn-wrapper.share-btn::after {
  display: none;
}
