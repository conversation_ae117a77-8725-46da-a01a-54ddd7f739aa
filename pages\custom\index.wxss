/* 自定义页面样式 */
.custom-page-container {
  min-height: 100vh;
  background-color: #f8faff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

/* webview容器样式 */
.webview-container {
  width: 100%;
  height: 100vh;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #3e7fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 30rpx;
}

.retry-btn, .back-btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.retry-btn {
  background-color: #3e7fff;
  color: white;
}

.back-btn {
  background-color: #f5f5f5;
  color: #333;
}

.retry-btn:active {
  background-color: #2c5ce6;
}

.back-btn:active {
  background-color: #e5e5e5;
}

/* 内容状态样式 */
.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.page-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}
