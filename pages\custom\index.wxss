/* pages/custom/index.wxss */
.custom-page {
  min-height: 100vh;
  background-color: #f8faff;
  padding-bottom: 160rpx; /* 为底部导航栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #3e7fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #999;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #3e7fff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 内容容器 */
.content-container {
  padding: 40rpx 30rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.company-name {
  font-size: 28rpx;
  color: #666;
}

/* 自定义内容区域 */
.custom-content {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 默认内容 */
.default-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 80rpx 40rpx;
}

.default-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.default-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.default-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 自定义页面内容 */
.custom-page-content {
  min-height: 400rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部导航栏 */
.nav-bar {
  position: fixed;
  bottom: 80rpx;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20rpx 0;
  max-width: 100%;
}

.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx;
  min-height: 80rpx;
}

.nav-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 导航图标样式 */
.nav-icon-card {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDRINEMyLjkgNCAyIDQuOSAyIDZWMThDMiAxOS4xIDIuOSAyMCA0IDIwSDIwQzIxLjEgMjAgMjIgMTkuMSAyMiAxOFY2QzIyIDQuOSAyMS4xIDQgMjAgNFpNMjAgMThINFY2SDE2VjhIMjBWMThaIiBmaWxsPSIjNjY2Ii8+Cjwvc3ZnPgo=');
}

.nav-icon-home {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDIwVjE0SDEyVjIwSDIxVjEySDI0TDEyIDJMMCAxMkgzVjIwSDEwWiIgZmlsbD0iIzY2NiIvPgo8L3N2Zz4K');
}

.nav-icon-personal {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIxIDEyIDE2IDEwLjIxIDE2IDhDMTYgNS43OSAxNC4yMSA0IDEyIDRDOS43OSA0IDggNS43OSA4IDhDOCAxMC4yMSA5Ljc5IDEyIDEyIDEyWk0xMiAxNEM5LjMzIDE0IDQgMTUuMzQgNCAyMFYyMkgyMFYyMEMyMCAxNS4zNCAxNC42NyAxNCAxMiAxNFoiIGZpbGw9IiM2NjYiLz4KPC9zdmc+Cg==');
}

.nav-icon-enterprise {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDdWM0g0VjIxSDIwVjdIMTJaTTYgMTlINFYxN0g2VjE5Wk02IDE1SDRWMTNINlYxNVpNNiAxMUg0VjlINlYxMVpNNiA3SDRWNUg2VjdaTTEwIDE5SDhWMTdIMTBWMTlaTTEwIDE1SDhWMTNIMTBWMTVaTTEwIDExSDhWOUgxMFYxMVpNMTAgN0g4VjVIMTBWN1pNMTggMTlIMTZWMTdIMThWMTlaTTE4IDE1SDE2VjEzSDE4VjE1Wk0xOCAxMUgxNlY5SDE4VjExWiIgZmlsbD0iIzY2NiIvPgo8L3N2Zz4K');
}

.nav-icon-products {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMThDNS45IDE4IDUuMDEgMTguOSA1LjAxIDIwUzUuOSAyMiA3IDIyIDkgMjEuMSA5IDIwIDguMSAxOCA3IDE4Wk0xIDJWNEgzTDYuNiAxMS41OUw1LjI1IDE0LjA0QzUuMDkgMTQuMzIgNSAxNC42NSA1IDE1QzUgMTYuMSA1LjkgMTcgNyAxN0gxOVYxNUg3LjQyQzcuMjggMTUgNy4xNyAxNC44OSA3LjE3IDE0Ljc1TDcuMiAxNC42M0w4LjEgMTNIMTUuNTVDMTYuMyAxMyAxNi45NiAxMi41OSAxNy4zIDExLjk3TDIwLjg4IDVIMTguN0wxNS41NSAxMUg4LjUzTDQuMjcgMkgxWk0xNyAxOEMxNS45IDE4IDE1LjAxIDE4LjkgMTUuMDEgMjBTMTUuOSAyMiAxNyAyMlMxOSAyMS4xIDE5IDIwUzE4LjEgMTggMTcgMThaIiBmaWxsPSIjNjY2Ii8+Cjwvc3ZnPgo=');
}

.nav-icon-share {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE4IDEyQzE2LjkgMTIgMTYgMTIuOSAxNiAxNEMxNiAxNC4xIDE2IDE0LjIgMTYuMDEgMTQuM0w5LjY5IDEwLjY5QzEwLjE5IDEwLjE5IDEwLjUgOS41NCAxMC41IDhDMTAuNSA2LjkgOS42IDYgOC41IDZTNi41IDYuOSA2LjUgOFM3LjQgMTAgOC41IDEwQzguNjUgMTAgOC43OSA5Ljk3IDguOTMgOS45NEwxNS4zMSAxMy42OUMxNS4xMSAxNC4xIDE1IDE0LjU0IDE1IDE1QzE1IDE2LjEgMTUuOSAxNyAxNyAxN1MxOSAxNi4xIDE5IDE1UzE4LjEgMTMgMTcgMTNDMTYuODUgMTMgMTYuNzEgMTMuMDMgMTYuNTcgMTMuMDZMOS45MyA5LjMxQzEwLjEzIDguOSAxMC4yNSA4LjQ2IDEwLjI1IDhDMTAuMjUgNi45IDkuMzUgNiA4LjI1IDZTNi4yNSA2LjkgNi4yNSA4UzcuMTUgMTAgOC4yNSAxMEM4LjQgMTAgOC41NCAxMC4wMyA4LjY4IDEwLjA2TDE1LjMyIDEzLjgxQzE1LjEyIDE0LjIyIDE1IDE0LjYgMTUgMTVDMTUgMTYuMSAxNS45IDE3IDE3IDE3UzE5IDE2LjEgMTkgMTVTMTguMSAxMyAxNyAxM1oiIGZpbGw9IiM2NjYiLz4KPC9zdmc+Cg==');
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .nav-text {
    font-size: 18rpx;
  }
  
  .nav-icon {
    width: 28rpx;
    height: 28rpx;
  }
}
