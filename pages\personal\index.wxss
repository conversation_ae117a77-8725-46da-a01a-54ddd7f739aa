.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8faff;
  box-sizing: border-box;
}

/* 登录页专用容器样式 */
.login-page-container {
  min-height: 100vh;
  background-color: #f8faff;
  box-sizing: border-box;
}

/* 登录页样式 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100vh;
  padding: 60rpx 50rpx;
  box-sizing: border-box;
  position: relative;
  background: linear-gradient(135deg, #f6f9ff 0%, #ffffff 100%);
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: -200rpx;
  right: -200rpx;
  width: 600rpx;
  height: 600rpx;
  border-radius: 300rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.08) 0%, rgba(62, 127, 255, 0.03) 100%);
  z-index: 0;
}

.login-container::after {
  content: '';
  position: absolute;
  bottom: -300rpx;
  left: -200rpx;
  width: 700rpx;
  height: 700rpx;
  border-radius: 350rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.06) 0%, rgba(62, 127, 255, 0.02) 100%);
  z-index: 0;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  margin-top: 120rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  filter: drop-shadow(0 8rpx 16rpx rgba(62, 127, 255, 0.2));
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  background: linear-gradient(90deg, #3E7FFF, #6B9FFF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.subtitle {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 0;
}

/* 功能特性区域 */
.login-features {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 60rpx 0;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.8);
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(62, 127, 255, 0.08);
}

.feature-icon {
  width: 56rpx;
  height: 56rpx;
  margin-right: 20rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 底部登录区域 */
.login-footer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  margin-bottom: 80rpx;
  margin-top: -40rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  color: #fff;
  font-size: 32rpx;
  border-radius: 48rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
  margin: 0;
  margin-bottom: 20rpx;
}

.login-btn-active {
  background: linear-gradient(90deg, #07C160, #10D86B);
  box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.2);
}

.login-btn-disabled {
  background: linear-gradient(90deg, #B7B7B7, #CCCCCC);
  box-shadow: 0 8rpx 20rpx rgba(150, 150, 150, 0.2);
}

.login-btn::after {
  border: none;
}

.wechat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  filter: brightness(0) invert(1);
}

.login-text {
  height: 32rpx;
  line-height: 32rpx;
  font-size: 32rpx;
  color: #fff;
}

.privacy-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

.privacy-link {
  color: #3E7FFF;
}

/* 协议勾选区样式 */
.agreement-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.agreement-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.agreement-checkbox {
  transform: scale(0.7);
  margin-right: 10rpx;
}

/* 名片区域样式 */
.card-container {
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
}

.card-wrapper {
  margin-bottom: 30rpx;
  width: 100%;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(62, 127, 255, 0.1);
}

/* 空名片样式 */
.empty-card {
  background-color: #fff;
  box-shadow: 0 8rpx 30rpx rgba(62, 127, 255, 0.1);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  position: relative;
  overflow: hidden;
}

.empty-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.05) 0%, rgba(62, 127, 255, 0.1) 100%);
  border-radius: 0 0 0 100%;
  z-index: 0;
}

.empty-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 150rpx;
  height: 150rpx;
  background: linear-gradient(135deg, rgba(62, 127, 255, 0.08) 0%, rgba(62, 127, 255, 0.02) 100%);
  border-radius: 0 100% 0 0;
  z-index: 0;
}

.empty-card-text {
  font-size: 34rpx;
  color: #3E7FFF;
  font-weight: 500;
  position: relative;
  z-index: 1;
  margin-top: 30rpx;
}

.empty-card-icon {
  width: 80rpx;
  height: 80rpx;
  position: relative;
  z-index: 1;
}

/* 操作区域样式 */
.action-area {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(62, 127, 255, 0.1);
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  align-items: flex-start;
  /* 支持自定义页面按钮的动态布局 */
  flex-wrap: nowrap;
  min-height: 120rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20rpx 0;
  flex: 1;
  background-color: transparent;
  line-height: normal;
  font-weight: normal;
  font-size: unset;
  position: relative;
  box-sizing: border-box;
}

.action-item::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: #f0f0f0;
}

.action-item:last-child::after {
  display: none;
}

.action-text {
  font-size: 28rpx;
  color: #333;
  margin-top: 12rpx;
  text-align: center;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
}

.share-btn {
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  width: 100% !important;
  height: auto !important;
  border: none !important;
  border-radius: 0 !important;
  font-size: inherit !important;
  color: inherit !important;
  line-height: normal !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
  text-align: center !important;
  overflow: visible !important;
}

.share-btn::after {
  display: none !important;
  border: none !important;
}

button.share-btn {
  position: relative;
  box-shadow: none;
}

.share-btn .action-icon {
  width: 42rpx;
  height: 42rpx;
  margin-bottom: 0;
  flex-shrink: 0;
}

.share-btn .action-text {
  margin-top: 12rpx;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.share-btn-generating {
  color: #999 !important;
  opacity: 0.8;
}

/* 自定义页面按钮样式优化 */
.action-item[wx:if*="customPage"] {
  /* 确保自定义页面按钮与其他按钮保持一致的样式 */
  min-width: 0;
  flex-shrink: 1;
}

/* 当有4个按钮时的布局优化 - 使用兼容的选择器 */
.action-area .action-item {
  flex: 1;
  min-width: 0;
  max-width: 25%;
}

/* 自定义页面按钮文字样式 */
.action-item .action-text {
  word-break: break-all;
  /* 移除不支持的 hyphens 属性 */
}

/* 占位符样式 */
.placeholder {
  visibility: hidden;
}

/* 自定义设置按钮样式 */
.settings-button {
  position: fixed;
  bottom: 260rpx;
  right: 40rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  border: 1rpx solid rgba(230, 230, 230, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.settings-button:active {
  transform: scale(0.95);
  background: rgba(245, 245, 245, 0.9);
}

.settings-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.7;
}

.settings-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 400;
}

/* 退出登录按钮样式 */
.logout-button {
  position: fixed;
  bottom: 190rpx;
  right: 40rpx;
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  border: 1rpx solid rgba(230, 230, 230, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.logout-button:active {
  transform: scale(0.95);
  background: rgba(245, 245, 245, 0.9);
}

.logout-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.7;
}

.logout-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 400;
}

/* 联系我们按钮样式 */
.contact-us-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: linear-gradient(90deg, rgba(62, 127, 255, 0.1), rgba(107, 159, 255, 0.1));
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(62, 127, 255, 0.1);
  border: 1rpx solid rgba(62, 127, 255, 0.2);
}

.contact-us-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.contact-us-text {
  font-size: 28rpx;
  color: #3E7FFF;
  line-height: 40rpx;
} 