<view class="container">
  <!-- 标题和分享按钮 -->
  <view class="title-container">
    <view class="title">{{productsName}}</view>
    <button class="page-share-button" open-type="share" size="mini" catchtap="shareProductCenter">
      <image class="share-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png" mode="aspectFit"></image>
      <text>分享此页</text>
    </button>
  </view>
  
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 产品列表 -->
  <view class="products-list" wx:if="{{!loading && productsList.length > 0}}">
    <view class="product-item" wx:for="{{productsList}}" wx:key="id" bindtap="viewProduct" data-id="{{item.id}}" data-url="{{item.productUrl}}" data-name="{{item.name}}" data-image="{{item.shareImage || item.coverImage}}">
      <!-- 产品封面图 -->
      <view class="product-cover">
        <image class="cover-image" src="{{item.coverImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-project.png'}}" mode="aspectFill"></image>
      </view>
      
      <!-- 产品信息 -->
      <view class="product-info">
        <view class="product-name">{{item.name}}</view>
        <view class="product-description">{{item.description || '暂无产品简介'}}</view>
        
        <!-- 操作按钮 -->
        <view class="product-actions">
          <button class="action-button share-button" open-type="share" size="mini" data-id="{{item.id}}" data-name="{{item.name}}" data-image="{{item.shareImage || item.coverImage}}" data-url="{{item.productUrl}}" catchtap="shareProduct">
            <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png" mode="aspectFit"></image>
            <text>分享</text>
          </button>
          <button class="action-button view-button" size="mini" data-id="{{item.id}}" data-url="{{item.productUrl}}" data-name="{{item.name}}" data-image="{{item.shareImage || item.coverImage}}" catchtap="viewProduct">
            <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/view.png" mode="aspectFit"></image>
            <text>查看</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 无产品提示 -->
  <view class="empty-container" wx:if="{{!loading && productsList.length === 0}}">
    <image class="empty-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/empty-projects.png" mode="aspectFit"></image>
    <view class="empty-text">暂无产品</view>
  </view>
  
  <!-- 右侧导航栏 - 只在showNavBar为true时显示 -->
  <view class="nav-bar {{isNavCollapsed ? 'collapsed' : ''}}" wx:if="{{showNavBar}}">
      <!-- 显示导航按钮 - 当导航栏收起时显示 -->
      <view class="toggle-btn" bindtap="showNav" hidden="{{!isNavCollapsed}}">
        <view class="toggle-text vertical">
          <view class="toggle-char">显</view>
          <view class="toggle-char">示</view>
          <view class="toggle-char">导</view>
          <view class="toggle-char">航</view>
        </view>
      </view>
      
      <!-- 隐藏导航按钮 - 当导航栏展开时显示 -->
      <view class="toggle-btn" bindtap="hideNav" hidden="{{isNavCollapsed}}">
        <view class="toggle-text vertical">
          <view class="toggle-char">隐</view>
          <view class="toggle-char">藏</view>
          <view class="toggle-char">导</view>
          <view class="toggle-char">航</view>
        </view>
      </view>
      
      <!-- 导航按钮区域 -->
      <view class="nav-buttons" hidden="{{isNavCollapsed}}">
        <!-- 返回名片按钮，仅当从分享进入且有cardId时显示 -->
        <view class="nav-btn" wx:if="{{fromShare && cardId}}" bindtap="goBackToCard">
          <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></image>
          <view class="btn-text">返回名片</view>
        </view>

        <!-- 返回个人页面按钮，当从个人视角进入时显示 -->
        <view class="nav-btn" wx:if="{{!fromShare}}" bindtap="goBackToPersonal">
          <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></image>
          <view class="btn-text">个人页面</view>
        </view>

        <!-- 返回主页按钮，仅当从分享进入且有companyCode时显示 -->
        <view class="nav-btn" wx:if="{{fromShare && companyCode}}" bindtap="goToEnterprise">
          <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png"></image>
          <view class="btn-text">返回主页</view>
        </view>

        <!-- 企业页面按钮，当从个人视角进入且有企业信息时显示 -->
        <view class="nav-btn" wx:if="{{!fromShare && companyCode}}" bindtap="goToEnterprise">
          <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png"></image>
          <view class="btn-text">企业页面</view>
        </view>

        <!-- 自定义页面按钮 -->
        <view class="nav-btn" wx:if="{{companyInfo && companyInfo.customPage}}" bindtap="goToCustomPage">
          <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/custom-default.png"></image>
          <view class="btn-text">{{customName || '新页面'}}</view>
        </view>

        <!-- 分享按钮 - 使用button的open-type="share"直接调起分享功能 -->
        <button class="nav-btn-wrapper share-btn" open-type="share">
          <view class="nav-btn">
            <image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png"></image>
            <view class="btn-text">分享</view>
          </view>
        </button>
      </view>
  </view>
</view>