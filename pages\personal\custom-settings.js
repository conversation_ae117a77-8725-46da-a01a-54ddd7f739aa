const app = getApp()

Page({
  data: {
    enterpriseName: '企业',
    productsName: '产品中心',
    customName: '新页面',
    isLoading: true,
    isSaving: false,
    hasCompany: false, // 用户是否关联了企业
    enterpriseShareImage: '', // 企业分享图
    productsShareImage: '',   // 产品中心分享图
    customShareImage: ''      // 自定义页面分享图
  },

  onLoad() {
    this.loadCustomNames();
  },

  // 加载自定义名称
  loadCustomNames() {
    this.setData({ isLoading: true });
    
    // 检查用户是否已登录
    if (!app.globalData.userInfo || !app.globalData.userInfo.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      this.setData({ isLoading: false });
      return;
    }
    
    // 导入公司状态管理模块
    const companyStore = require('../../store/company.js');
    
    // 检查用户是否关联了企业
    if (!app.globalData.userInfo.companyCode) {
      this.setData({
        isLoading: false,
        hasCompany: false
      });
      return;
    }
    
    // 设置已关联企业标志
    this.setData({
      hasCompany: true
    });
    
    // 如果全局数据中已有企业信息，直接使用
    if (app.globalData.companyInfo) {
      this.setData({
        enterpriseName: app.globalData.companyInfo.enterpriseName || '企业',
        productsName: app.globalData.companyInfo.productsName || '产品中心',
        customName: app.globalData.companyInfo.customName || '新页面',
        enterpriseShareImage: app.globalData.companyInfo.enterpriseShareImage || '',
        productsShareImage: app.globalData.companyInfo.productsShareImage || '',
        customShareImage: app.globalData.companyInfo.customShareImage || '',
        isLoading: false
      });
      return;
    }
    
    // 否则请求企业信息
    companyStore.getCompanyCustomNames().then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          enterpriseName: res.data.enterpriseName || '企业',
          productsName: res.data.productsName || '产品中心',
          customName: res.data.customName || '新页面',
          enterpriseShareImage: res.data.enterpriseShareImage || '',
          productsShareImage: res.data.productsShareImage || '',
          customShareImage: res.data.customShareImage || '',
          isLoading: false
        });
      } else {
        this.setData({
          isLoading: false
        });
      }
    }).catch(err => {
      console.error('获取企业自定义名称失败', err);
      this.setData({
        isLoading: false
      });
      wx.showToast({
        title: '获取设置失败',
        icon: 'none'
      });
    });
  },
  
  // 输入企业名称
  onEnterpriseNameInput(e) {
    this.setData({
      enterpriseName: e.detail.value
    });
  },
  
  // 输入产品中心名称
  onProductsNameInput(e) {
    this.setData({
      productsName: e.detail.value
    });
  },
  
  // 预览企业分享图
  previewEnterpriseShareImage() {
    const imageUrl = this.data.enterpriseShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-enterprise.jpg';
    
    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },
  
  // 预览产品中心分享图
  previewProductsShareImage() {
    const imageUrl = this.data.productsShareImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/shared-products.jpg';
    
    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },
  
  // 保存设置
  saveSettings() {
    if (this.data.isSaving) return;
    
    // 检查输入是否为空
    if (!this.data.enterpriseName.trim()) {
      wx.showToast({
        title: '企业名称不能为空',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.productsName.trim()) {
      wx.showToast({
        title: '产品中心名称不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 检查名称长度
    if (this.data.enterpriseName.length > 30) {
      wx.showToast({
        title: '企业名称不能超过30个字符',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.productsName.length > 30) {
      wx.showToast({
        title: '产品中心名称不能超过30个字符',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ isSaving: true });
    wx.showLoading({ title: '保存中...' });
    
    // 导入公司状态管理模块
    const companyStore = require('../../store/company.js');
    
    // 调用接口更新自定义名称
    companyStore.updateCompanyCustomNames(
      this.data.enterpriseName.trim(),
      this.data.productsName.trim()
    ).then(res => {
      if (res.code === 0) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 更新全局数据
        if (app.globalData.companyInfo) {
          app.globalData.companyInfo.enterpriseName = this.data.enterpriseName.trim();
          app.globalData.companyInfo.productsName = this.data.productsName.trim();
        }
        
        // 设置上一个页面需要刷新的标志
        const pages = getCurrentPages();
        if (pages.length > 1) {
          const prevPage = pages[pages.length - 2];
          if (prevPage && prevPage.route === 'pages/personal/index') {
            prevPage.setData({
              needRefresh: true
            });
          }
        }
        
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('更新企业自定义名称失败', err);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ isSaving: false });
      wx.hideLoading();
    });
  }
}) 