const app = getApp();
const request = require('../utils/request.js');

/**
 * 获取当前用户关联的企业信息
 * @returns {Promise} 企业信息请求Promise
 */
const getCompanyInfo = () => {
  return new Promise((resolve, reject) => {
    // 如果全局已有企业信息，直接返回
    if (app.globalData.companyInfo) {
      resolve({
        code: 0,
        message: 'success',
        data: app.globalData.companyInfo
      });
      return;
    }

    // 检查用户是否已登录
    if (!app.globalData.userInfo || !app.globalData.userInfo.userId) {
      resolve({
        code: 0,
        message: 'user not logged in',
        data: null
      });
      return;
    }

    // 检查用户是否有关联的企业编码
    if (!app.globalData.userInfo.companyCode) {
      resolve({
        code: 0,
        message: 'success',
        data: null
      });
      return;
    }

    request.get('/company/info').then(res => {
      if (res.code === 0 && res.data) {
        app.globalData.companyInfo = res.data;
      }
      resolve(res);
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 获取企业专属页面路径
 * 根据用户关联的企业编码获取对应的企业页面和产品中心页面路径
 * @returns {Promise} 企业页面路径请求Promise
 */
const getCompanyPages = () => {
  return new Promise((resolve, reject) => {
    getCompanyInfo().then(res => {
      if (res.code === 0 && res.data) {
        resolve({
          code: 0,
          message: 'success',
          data: {
            enterprisePage: res.data.enterprisePage || null,
            productsPage: res.data.productsPage || null
          }
        });
      } else {
        resolve({
          code: 0,
          message: 'success',
          data: {
            enterprisePage: null,
            productsPage: null
          }
        });
      }
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 获取企业自定义名称
 * 根据用户关联的企业编码获取对应的企业名称和产品中心名称
 * @param {String} companyCode 可选，企业编码。不传则使用当前用户关联的企业
 * @returns {Promise} 企业自定义名称请求Promise
 */
const getCompanyCustomNames = (companyCode) => {
  return new Promise((resolve, reject) => {
    // 如果已有缓存且不传入companyCode，直接返回缓存
    if (!companyCode && app.globalData.companyInfo) {
      resolve({
        code: 0,
        message: 'success',
        data: {
          enterpriseName: app.globalData.companyInfo.enterpriseName || '企业',
          productsName: app.globalData.companyInfo.productsName || '产品中心',
          enterpriseShareImage: app.globalData.companyInfo.enterpriseShareImage || '',
          productsShareImage: app.globalData.companyInfo.productsShareImage || ''
        }
      });
      return;
    }

    // 检查用户是否已登录
    if (!companyCode && (!app.globalData.userInfo || !app.globalData.userInfo.userId)) {
      resolve({
        code: 0,
        message: 'user not logged in',
        data: {
          enterpriseName: '企业',
          productsName: '产品中心',
          enterpriseShareImage: '',
          productsShareImage: ''
        }
      });
      return;
    }

    // 检查用户是否有关联的企业编码
    if (!companyCode && !app.globalData.userInfo.companyCode) {
      resolve({
        code: 0,
        message: 'success',
        data: {
          enterpriseName: '企业',
          productsName: '产品中心',
          enterpriseShareImage: '',
          productsShareImage: ''
        }
      });
      return;
    }

    // 构建请求参数
    const params = {};
    if (companyCode) {
      params.companyCode = companyCode;
    }

    request.get('/company/info', params).then(res => {
      if (res.code === 0 && res.data) {
        // 如果是当前用户的企业，更新全局缓存
        if (!companyCode && app.globalData.userInfo && app.globalData.userInfo.companyCode === res.data.companyCode) {
          app.globalData.companyInfo = res.data;
        }
        resolve({
          code: 0,
          message: 'success',
          data: {
            enterpriseName: res.data.enterpriseName || '企业',
            productsName: res.data.productsName || '产品中心',
            enterpriseShareImage: res.data.enterpriseShareImage || '',
            productsShareImage: res.data.productsShareImage || ''
          }
        });
      } else {
        resolve({
          code: 0,
          message: 'success',
          data: {
            enterpriseName: '企业',
            productsName: '产品中心',
            enterpriseShareImage: '',
            productsShareImage: ''
          }
        });
      }
    }).catch(err => {
      // 出错时返回默认值
      resolve({
        code: 0,
        message: 'success',
        data: {
          enterpriseName: '企业',
          productsName: '产品中心',
          customName: '新页面',
          enterpriseShareImage: '',
          productsShareImage: '',
          customShareImage: ''
        }
      });
    });
  });
};

/**
 * 更新企业页面路径(后台维护使用，一般通过数据库直接维护)
 * @param {String} companyCode 企业编码
 * @param {String} enterprisePage 企业页面路径
 * @param {String} productsPage 产品中心页面路径
 * @returns {Promise} 更新企业页面路径请求Promise
 */
const updateCompanyPages = (companyCode, enterprisePage, productsPage) => {
  return request.put('/company/pages', {
    companyCode,
    enterprisePage,
    productsPage
  });
};

/**
 * 更新企业自定义名称
 * @param {String} enterpriseName 企业自定义名称
 * @param {String} productsName 产品中心自定义名称
 * @param {String} customName 自定义页面名称
 * @param {String} companyCode 可选，企业编码。不传则使用当前用户关联的企业
 * @returns {Promise} 更新企业自定义名称请求Promise
 */
const updateCompanyCustomNames = (enterpriseName, productsName, customName, companyCode) => {
  const data = {};
  if (enterpriseName !== undefined) data.enterpriseName = enterpriseName;
  if (productsName !== undefined) data.productsName = productsName;
  if (customName !== undefined) data.customName = customName;
  if (companyCode) data.companyCode = companyCode;

  return new Promise((resolve, reject) => {
    request.put('/company/custom-names', data).then(res => {
      // 由于request.js中的resolve(data.data)，这里的res已经是响应的data字段
      
      // 更新全局缓存
      clearCompanyCache();
      
      // 设置一个全局标志，表示自定义名称已更新
      app.globalData.customNamesUpdated = true;
      
      // 如果有全局companyInfo，立即更新它，而不是等待下次请求
      if (app.globalData.companyInfo) {
        if (enterpriseName !== undefined) {
          app.globalData.companyInfo.enterpriseName = enterpriseName;
        }
        if (productsName !== undefined) {
          app.globalData.companyInfo.productsName = productsName;
        }
        if (customName !== undefined) {
          app.globalData.companyInfo.customName = customName;
        }
      }
      
      // 尝试更新TabBar名称
      try {
        const pages = getCurrentPages();
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          if (currentPage && typeof currentPage.getTabBar === 'function') {
            const tabBar = currentPage.getTabBar();
            if (tabBar && typeof tabBar.updateNames === 'function') {
              tabBar.updateNames(
                enterpriseName || '企业',
                productsName || '产品中心'
              );
            }
          }
        }
      } catch (e) {
        console.error('更新TabBar名称失败:', e);
      }
      
      // 构造一个符合预期的响应对象
      resolve({
        code: 0,
        message: 'success',
        data: res
      });
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 更新企业分享图片
 * @param {String} enterpriseShareImage 企业页面分享图URL
 * @param {String} productsShareImage 产品中心分享图URL
 * @param {String} companyCode 企业编码
 * @returns {Promise} 更新企业分享图片请求Promise
 */
const updateCompanyShareImages = (enterpriseShareImage, productsShareImage, companyCode) => {
  const data = { companyCode };
  if (enterpriseShareImage !== undefined) data.enterpriseShareImage = enterpriseShareImage;
  if (productsShareImage !== undefined) data.productsShareImage = productsShareImage;

  return new Promise((resolve, reject) => {
    request.put('/company/share-images', data).then(res => {
      // 由于request.js中的resolve(data.data)，这里的res已经是响应的data字段
      // 清除缓存，以便下次获取最新数据
      clearCompanyCache();
      
      // 构造一个符合预期的响应对象
      resolve({
        code: 0,
        message: 'success',
        data: res
      });
    }).catch(err => {
      reject(err);
    });
  });
};

/**
 * 清除企业缓存信息
 */
const clearCompanyCache = () => {
  app.globalData.companyInfo = null;
};

module.exports = {
  getCompanyInfo,
  getCompanyPages,
  getCompanyCustomNames,
  updateCompanyPages,
  updateCompanyCustomNames,
  updateCompanyShareImages,
  clearCompanyCache
}; 