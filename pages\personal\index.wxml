<view class="container">
  <!-- 未登录状态的个人页面 -->
  <view class="card-container" wx:if="{{!hasUserInfo}}">
    <!-- 名片组件 -->
    <view class="card-wrapper" bindtap="promptLogin">
      <view class="empty-card">
        <image class="empty-card-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/add-card.png" mode="aspectFit"></image>
        <view class="empty-card-text">点击创建我的名片</view>
      </view>
    </view>

    <!-- 操作按钮区 -->
    <view class="action-area">
      <view class="action-item" bindtap="goToEnterpriseUnlogged">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png" mode="aspectFit"></image>
        <view class="action-text">{{enterpriseName}}</view>
      </view>
      <view class="action-item" bindtap="goToProductsUnlogged">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/products-default.png" mode="aspectFit"></image>
        <view class="action-text">{{productsName}}</view>
      </view>
      <view class="action-item" bindtap="promptLogin">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/edit.png" mode="aspectFit"></image>
        <view class="action-text">编辑名片</view>
      </view>
    </view>
    
    <!-- 联系我们按钮 -->
    <view class="contact-us-button" bindtap="showContactPopup">
      <image class="contact-us-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/customer-service.png" mode="aspectFit"></image>
      <text class="contact-us-text">需要定制化服务？请联系我们</text>
    </view>
  </view>

  <!-- 已登录状态的个人页面 -->
  <view class="card-container" wx:else>
    <!-- 名片组件 -->
    <view class="card-wrapper" bindtap="{{hasCardInfo ? '' : 'editCard'}}">
      <block wx:if="{{hasCardInfo}}">
        <business-card cardInfo="{{cardInfo}}" isOwner="{{true}}"></business-card>
      </block>
      <block wx:else>
        <view class="empty-card">
          <image class="empty-card-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/add-card.png" mode="aspectFit"></image>
          <view class="empty-card-text">点击创建我的名片</view>
        </view>
      </block>
    </view>

    <!-- 多名片操作按钮区 -->
    <view class="action-area" wx:if="{{hasCardInfo}}">
      <view class="action-item" bindtap="createNewCard">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/add-card.png" mode="aspectFit"></image>
        <view class="action-text">创建新名片</view>
      </view>
      <view class="action-item" bindtap="switchCard">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/switch.png" mode="aspectFit"></image>
        <view class="action-text">切换名片</view>
      </view>
      <view class="action-item" bindtap="editCard">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/edit.png" mode="aspectFit"></image>
        <view class="action-text">编辑名片</view>
      </view>
      <view class="action-item" bindtap="goToCustomSettings" wx:if="{{hasCompanyCode}}">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/settings.png" mode="aspectFit"></image>
        <view class="action-text">自定义编辑</view>
      </view>
    </view>

    <!-- 操作按钮区 -->
    <view class="action-area" wx:if="{{hasCardInfo}}">
      <!-- AI分身按钮 - 仅在有AI分身URL时显示 -->
      <view class="action-item" bindtap="goToAiAvatar" wx:if="{{aiAvatarUrl}}">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/customer-service.png" mode="aspectFit"></image>
        <view class="action-text">AI分身</view>
      </view>

      <view class="action-item" bindtap="goToEnterprise">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png" mode="aspectFit"></image>
        <view class="action-text">{{enterpriseName}}</view>
      </view>
      <view class="action-item" bindtap="goToProducts">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/products-default.png" mode="aspectFit"></image>
        <view class="action-text">{{productsName}}</view>
      </view>
      <view class="action-item" wx:if="{{companyInfo && companyInfo.customPage}}" bindtap="goToCustomPage">
        <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/products-default.png" mode="aspectFit"></image>
        <view class="action-text">{{customName}}</view>
      </view>
    </view>
    
    <!-- 分享按钮区 -->
    <view class="action-area" wx:if="{{hasCardInfo}}">
      <view class="action-item">
        <button class="share-btn" wx:if="{{companyInfo && companyInfo.companyCode}}" open-type="share" data-share-type="enterprise">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-share.png" mode="aspectFit"></image>
          <view class="action-text">分享{{enterpriseName}}</view>
        </button>
        <button class="share-btn" wx:else bindtap="goToEnterprise">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-share.png" mode="aspectFit"></image>
          <view class="action-text">分享{{enterpriseName}}</view>
        </button>
      </view>
      <view class="action-item">
        <button class="share-btn" open-type="share" data-share-type="products">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/products-share.png" mode="aspectFit"></image>
          <view class="action-text">分享{{productsName}}</view>
        </button>
      </view>
      <view class="action-item" wx:if="{{companyInfo && companyInfo.customPage}}">
        <button class="share-btn" open-type="share" data-share-type="custom">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/products-share.png" mode="aspectFit"></image>
          <view class="action-text">分享{{customName}}</view>
        </button>
      </view>
      <view class="action-item">
        <button class="share-btn {{isGeneratingShareImage ? 'share-btn-generating' : ''}}" open-type="{{isGeneratingShareImage ? '' : 'share'}}" disabled="{{isGeneratingShareImage}}">
          <image class="action-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png" mode="aspectFit"></image>
          <view class="action-text">{{isGeneratingShareImage ? '名片生成中' : '分享名片'}}</view>
        </button>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-button" bindtap="showLogoutConfirm">
      <image class="logout-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/logout.png" mode="aspectFit"></image>
      <text class="logout-text">退出登录</text>
    </view>
  </view>
  
  <!-- 用于生成分享图的canvas，设置为不可见 -->
  <canvas canvas-id="shareCanvas" style="width: 300px; height: 400px; position: fixed; left: -1000px; top: -1000px;"></canvas>
  
  <!-- 联系我们弹窗 -->
  <contact-popup show="{{showContactPopup}}" bindclose="closeContactPopup"></contact-popup>
</view> 