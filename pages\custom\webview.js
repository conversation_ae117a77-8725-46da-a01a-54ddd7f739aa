/**
 * 自定义页面webview
 */
Page({
  data: {
    url: '',
    customName: '自定义页面',
    companyCode: '',
    fromShare: false
  },

  onLoad(options) {
    console.log('自定义页面webview加载，参数:', options);
    
    const { url, customName, companyCode, fromShare } = options;
    
    if (url) {
      const decodedUrl = decodeURIComponent(url);
      console.log('解码后的URL:', decodedUrl);
      
      this.setData({
        url: decodedUrl,
        customName: customName || '自定义页面',
        companyCode: companyCode || '',
        fromShare: fromShare === 'true'
      });
    } else {
      wx.showToast({
        title: '链接地址无效',
        icon: 'none'
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  onShow() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.customName || '自定义页面'
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { customName, companyCode } = this.data;
    
    return {
      title: customName || '自定义页面',
      path: `/pages/custom/index?companyCode=${companyCode}&fromShare=true`,
      imageUrl: '' // 可以根据需要设置分享图片
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { customName, companyCode } = this.data;
    
    return {
      title: customName || '自定义页面',
      query: `companyCode=${companyCode}&fromShare=true`,
      imageUrl: '' // 可以根据需要设置分享图片
    };
  },

  /**
   * webview加载完成
   */
  onWebviewLoad(e) {
    console.log('webview加载完成:', e);
  },

  /**
   * webview加载失败
   */
  onWebviewError(e) {
    console.error('webview加载失败:', e);
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    });
  }
});
