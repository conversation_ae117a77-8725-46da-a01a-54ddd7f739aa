<!--自定义页面入口-->
<view class="custom-page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-title">页面暂不可用</text>
    <text class="error-message">该企业暂未设置自定义页面</text>
    <view class="error-actions">
      <button class="retry-btn" bindtap="reload">重新加载</button>
      <button class="back-btn" bindtap="goBack">返回</button>
    </view>
  </view>

  <!-- webview状态 -->
  <view wx:elif="{{showWebview}}" class="webview-container">
    <web-view
      src="{{customPage}}"
      bindload="onWebviewLoad"
      binderror="onWebviewError">
      <!-- 右侧导航栏 - 只在showNavBar为true时显示 -->
      <cover-view class="nav-bar {{isNavCollapsed ? 'collapsed' : ''}}" wx:if="{{showNavBar}}">
        <!-- 显示导航按钮 - 当导航栏收起时显示 -->
        <cover-view class="toggle-btn" bindtap="showNav" hidden="{{!isNavCollapsed}}">
          <cover-view class="toggle-text vertical">
            <cover-view class="toggle-char">显</cover-view>
            <cover-view class="toggle-char">示</cover-view>
            <cover-view class="toggle-char">导</cover-view>
            <cover-view class="toggle-char">航</cover-view>
          </cover-view>
        </cover-view>

        <!-- 隐藏导航按钮 - 当导航栏展开时显示 -->
        <cover-view class="toggle-btn" bindtap="hideNav" hidden="{{isNavCollapsed}}">
          <cover-view class="toggle-text vertical">
            <cover-view class="toggle-char">隐</cover-view>
            <cover-view class="toggle-char">藏</cover-view>
            <cover-view class="toggle-char">导</cover-view>
            <cover-view class="toggle-char">航</cover-view>
          </cover-view>
        </cover-view>

        <!-- 导航按钮区域 -->
        <cover-view class="nav-buttons" hidden="{{isNavCollapsed}}">
          <!-- 返回名片按钮，仅当从分享进入且有cardId时显示 -->
          <cover-view class="nav-btn" wx:if="{{fromShare && cardId}}" bindtap="goBackToCard">
            <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></cover-image>
            <cover-view class="btn-text">返回名片</cover-view>
          </cover-view>

          <!-- 返回个人页面按钮，当从个人视角进入时显示 -->
          <cover-view class="nav-btn" wx:if="{{!fromShare}}" bindtap="goBackToPersonal">
            <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/business-card.png"></cover-image>
            <cover-view class="btn-text">个人页面</cover-view>
          </cover-view>

          <!-- 企业页面按钮 -->
          <cover-view class="nav-btn" wx:if="{{companyCode}}" bindtap="goToEnterprise">
            <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/enterprise-default.png"></cover-image>
            <cover-view class="btn-text">企业页面</cover-view>
          </cover-view>

          <!-- 产品中心按钮 -->
          <cover-view class="nav-btn" wx:if="{{companyCode}}" bindtap="goToProducts">
            <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/default-products.png"></cover-image>
            <cover-view class="btn-text">产品中心</cover-view>
          </cover-view>

          <!-- 分享按钮 - 使用button的open-type="share"直接调起分享功能 -->
          <button class="nav-btn-wrapper share-btn" open-type="share">
            <cover-view class="nav-btn">
              <cover-image class="btn-icon" src="https://pic.sdtaa.com/ZhiLian/Picture/Project/icons/share.png"></cover-image>
              <cover-view class="btn-text">分享</cover-view>
            </cover-view>
          </button>
        </cover-view>
      </cover-view>
    </web-view>
  </view>
</view>
