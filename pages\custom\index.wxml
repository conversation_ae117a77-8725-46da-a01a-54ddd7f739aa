<!--自定义页面入口-->
<view class="custom-page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-title">页面暂不可用</text>
    <text class="error-message">该企业暂未设置自定义页面</text>
    <view class="error-actions">
      <button class="retry-btn" bindtap="reload">重新加载</button>
      <button class="back-btn" bindtap="goBack">返回</button>
    </view>
  </view>

  <!-- webview状态 -->
  <view wx:elif="{{showWebview}}" class="webview-container">
    <web-view
      src="{{customPage}}"
      bindload="onWebviewLoad"
      binderror="onWebviewError">
    </web-view>
  </view>
</view>
