<!--pages/custom/index.wxml-->
<view class="custom-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败</text>
    <button class="retry-btn" bindtap="onRetry">重试</button>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="page-title">{{customName}}</view>
      <view wx:if="{{companyInfo}}" class="company-name">{{companyInfo.name}}</view>
    </view>

    <!-- 自定义页面内容区域 -->
    <view class="custom-content">
      <view wx:if="{{!customPage}}" class="default-content">
        <view class="default-icon">📄</view>
        <text class="default-text">暂无自定义页面内容</text>
        <text class="default-desc">企业管理员可以设置自定义页面内容</text>
      </view>
      
      <view wx:else class="custom-page-content">
        <!-- 这里可以根据实际需求展示自定义页面内容 -->
        <text class="content-text">自定义页面内容加载中...</text>
      </view>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view wx:if="{{showNavBar && navBarButtons.length > 0}}" class="nav-bar">
    <view class="nav-buttons">
      <view 
        wx:for="{{navBarButtons}}" 
        wx:key="action"
        class="nav-button"
        data-action="{{item.action}}"
        bindtap="onNavButtonTap"
      >
        <view class="nav-icon nav-icon-{{item.icon}}"></view>
        <text class="nav-text">{{item.text}}</text>
      </view>
    </view>
  </view>
</view>
