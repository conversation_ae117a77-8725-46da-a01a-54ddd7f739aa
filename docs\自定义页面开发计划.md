# 自定义页面功能开发计划

## 项目概述
为每个公司增加一个自定义页面功能，支持URL、封面图、自定义名字，在个人名片页面产品中心右边显示按钮。

## 开发任务清单

### 阶段一：数据库设计与后端接口开发

#### 1. 数据库结构修改
- [x] 1.1 在companies表中添加自定义页面相关字段
  - [x] 添加 `custom_page VARCHAR(255)` 字段（自定义页面路径）
  - [x] 添加 `custom_name VARCHAR(30) DEFAULT '新页面'` 字段（自定义页面名称）
  - [x] 添加 `custom_share_image VARCHAR(255)` 字段（自定义页面分享图URL）
  - [x] 执行SQL语句并验证字段添加成功

#### 2. 后端数据模型更新
- [x] 2.1 更新Company模型
  - [x] 在 `server/models/company.model.js` 中添加新字段定义
  - [x] 验证模型字段映射正确

#### 3. 后端接口功能扩展
- [x] 3.1 修改企业信息获取接口
  - [x] 在 `server/controllers/company.controller.js` 的 `getCompanyInfo` 方法中添加自定义页面字段返回
  - [x] 测试接口返回数据包含 `customPage`、`customName`、`customShareImage` 字段

- [x] 3.2 修改自定义名称更新接口
  - [x] 在 `server/controllers/company.controller.js` 的 `updateCustomNames` 方法中添加 `customName` 字段处理
  - [x] 支持接收和更新自定义页面名称
  - [x] 保持30字符长度限制和自动截断功能
  - [x] 测试自定义页面名称更新功能

- [x] 3.3 修改企业页面路径更新接口（可选）
  - [x] 在 `updateCompanyPages` 方法中添加 `customPage` 字段处理
  - [x] 测试自定义页面路径更新功能

- [x] 3.4 修改分享图片更新接口（可选）
  - [x] 在 `updateShareImages` 方法中添加 `customShareImage` 字段处理
  - [x] 测试自定义页面分享图更新功能

### 阶段二：前端页面结构创建

#### 4. 创建自定义页面文件夹和基础文件
- [x] 4.1 创建自定义页面目录结构
  - [x] 创建 `pages/custom/` 文件夹
  - [x] 创建 `pages/custom/index.js`（自定义页面入口逻辑）
  - [x] 创建 `pages/custom/index.wxml`（自定义页面入口结构）
  - [x] 创建 `pages/custom/index.wxss`（自定义页面入口样式）
  - [x] 创建 `pages/custom/index.json`（自定义页面入口配置）

#### 5. 小程序配置更新
- [x] 5.1 更新app.json页面配置
  - [x] 在 `app.json` 的 `pages` 数组中添加自定义页面路径
  - [x] 添加 `"pages/custom/index"`

### 阶段三：前端功能实现

#### 6. 个人名片页面功能扩展
- [x] 6.1 修改个人名片页面布局
  - [x] 在 `pages/personal/index.wxml` 中产品中心按钮右边添加自定义页面按钮
  - [x] 设置按钮显示条件：只有当企业的 `customPage` 字段有值时才显示
  - [x] 按钮文字使用企业的 `customName` 字段

- [x] 6.2 修改个人名片页面逻辑
  - [x] 在 `pages/personal/index.js` 中添加自定义页面按钮点击事件处理
  - [x] 实现跳转到自定义页面的逻辑
  - [x] 支持传递必要参数（companyCode、fromShare等）

- [ ] 6.3 修改个人名片页面样式
  - [ ] 在 `pages/personal/index.wxss` 中添加自定义页面按钮样式
  - [ ] 确保按钮高度自适应文字内容
  - [ ] 保持与企业按钮和产品中心按钮的UI一致性

#### 7. 自定义设置页面功能扩展
- [x] 7.1 修改自定义设置页面结构
  - [x] 在 `pages/personal/custom-settings.wxml` 中添加自定义页面名称输入框
  - [x] 添加自定义页面分享图预览区域
  - [x] 位置：在企业名称和产品中心名称输入框下方

- [x] 7.2 修改自定义设置页面逻辑
  - [x] 在 `pages/personal/custom-settings.js` 中添加自定义页面名称字段处理
  - [x] 修改页面加载逻辑，获取并显示当前的自定义页面名称
  - [x] 修改保存逻辑，支持提交自定义页面名称到后端
  - [x] 添加自定义页面分享图预览功能

- [ ] 7.3 修改自定义设置页面样式
  - [ ] 在 `pages/personal/custom-settings.wxss` 中添加自定义页面相关样式
  - [ ] 确保UI布局协调一致

#### 8. 数据存储和状态管理
- [x] 8.1 修改企业数据存储
  - [x] 在 `store/company.js` 中添加自定义页面相关字段
  - [x] 修改 `updateCompanyCustomNames` 方法，支持更新自定义页面名称
  - [x] 添加获取和缓存自定义页面信息的方法

- [x] 8.2 修改数据同步机制
  - [x] 确保自定义页面名称修改后同步更新全局状态
  - [x] 使用与企业名称和产品中心名称相同的数据同步机制
  - [x] 保存成功后设置 `needRefresh` 标志

### 阶段四：自定义页面核心功能实现

#### 9. 自定义页面入口逻辑实现
- [x] 9.1 实现自定义页面入口逻辑
  - [x] 在 `pages/custom/index.js` 中实现页面加载逻辑
  - [x] 根据companyCode获取企业自定义页面信息
  - [x] 实现跳转到webview页面加载 `customPage` URL的逻辑
  - [x] 处理无权限访问和错误情况
  - [x] 添加分享按钮，按钮文字为：`分享 + customName`

#### 10. 分享功能实现
- [x] 10.1 添加自定义页面分享功能
  - [x] 在自定义页面中添加分享按钮，按钮名称格式：`分享 + customName`
  - [x] 实现分享逻辑，分享卡片使用 `customShareImage` 作为封面图
  - [x] 分享卡片标题使用 `customName` 参数
  - [x] 分享链接指向webview页面，传递 `customPage` URL参数

- [x] 10.2 修改分享工具类
  - [x] 在 `utils/share.js` 中添加自定义页面分享方法
  - [x] 确保分享链接包含正确的参数（customPage URL、companyCode等）
  - [x] 处理分享图片和标题的动态生成

- [x] 10.3 修改webview页面支持自定义页面
  - [x] 在 `pages/webview/index.js` 中添加对自定义页面URL的处理
  - [x] 支持接收和加载 `customPage` 参数中的URL
  - [x] 添加必要的错误处理和加载状态

### 阶段五：测试与优化

#### 11. 功能测试
- [ ] 11.1 数据库操作测试
  - [ ] 测试自定义页面字段的增删改查
  - [ ] 验证数据完整性和约束条件

- [ ] 11.2 后端接口测试
  - [ ] 测试企业信息获取接口返回自定义页面数据
  - [ ] 测试自定义名称更新接口
  - [ ] 测试各种边界情况和错误处理

- [ ] 11.3 前端功能测试
  - [ ] 测试个人名片页面自定义按钮显示逻辑（有URL时显示，无URL时隐藏）
  - [ ] 测试自定义设置页面的保存和加载功能
  - [ ] 测试自定义页面的跳转和webview加载功能
  - [ ] 测试自定义页面分享功能（分享按钮名称、分享图片、分享标题）
  - [ ] 测试分享卡片点击后的webview加载功能
  - [ ] 测试不同场景下的用户体验

- [ ] 11.4 集成测试
  - [ ] 测试完整的用户操作流程
  - [ ] 测试数据同步和状态管理
  - [ ] 测试分享功能（如已实现）

#### 12. UI/UX优化
- [ ] 12.1 样式优化
  - [ ] 确保自定义页面按钮与其他按钮样式一致
  - [ ] 优化长文本显示和截断效果
  - [ ] 适配不同屏幕尺寸

- [ ] 12.2 交互优化
  - [ ] 优化页面加载和跳转动画
  - [ ] 添加必要的加载状态和错误提示
  - [ ] 优化用户操作反馈

#### 13. 文档更新
- [ ] 13.1 更新开发文档
  - [ ] 在 `docs/开发文档.md` 中添加自定义页面功能说明
  - [ ] 更新页面结构和功能描述

- [ ] 13.2 更新接口文档
  - [ ] 在 `docs/后端接口设计.md` 中添加自定义页面相关接口
  - [ ] 更新数据模型和字段说明

- [ ] 13.3 更新数据库文档
  - [ ] 在 `docs/数据库设计.sql` 中添加自定义页面字段说明
  - [ ] 更新表结构和字段注释

### 阶段六：部署与发布

#### 14. 部署准备
- [ ] 14.1 生产环境数据库更新
  - [ ] 在生产环境执行数据库结构更新SQL
  - [ ] 验证生产环境数据完整性

- [ ] 14.2 后端代码部署
  - [ ] 部署更新后的后端代码到生产服务器
  - [ ] 验证后端接口功能正常

- [ ] 14.3 前端代码发布
  - [ ] 构建小程序代码包
  - [ ] 提交微信小程序审核
  - [ ] 发布上线

#### 15. 上线验证
- [ ] 15.1 功能验证
  - [ ] 验证自定义页面功能在生产环境正常工作
  - [ ] 测试各种用户场景和边界情况

- [ ] 15.2 性能监控
  - [ ] 监控新功能对系统性能的影响
  - [ ] 收集用户反馈和使用数据

## 开发注意事项

1. **数据一致性**：确保自定义页面数据与企业数据的一致性
2. **权限控制**：确保只有企业相关用户可以修改自定义页面设置
3. **UI一致性**：保持与现有企业页面和产品中心页面的UI风格一致
4. **性能优化**：避免不必要的数据加载和页面渲染
5. **错误处理**：完善各种异常情况的处理逻辑
6. **向后兼容**：确保新功能不影响现有功能的正常使用
7. **分享功能**：
   - 分享按钮名称格式：`分享 + customName`
   - 分享卡片封面图使用 `customShareImage`
   - 分享卡片标题使用 `customName`
   - 分享链接通过webview加载 `customPage` URL
8. **webview集成**：确保自定义页面URL能在微信小程序webview中正常加载

## 预计开发时间

- 阶段一：2-3天
- 阶段二：1天
- 阶段三：3-4天
- 阶段四：1-2天
- 阶段五：2-3天
- 阶段六：1天

**总计预计时间：10-14天**

---

**创建时间**：2025-01-16  
**最后更新**：2025-01-16  
**状态**：开发中
