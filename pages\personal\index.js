const app = getApp()

Page({
  data: {
    userInfo: null,
    cardInfo: null,
    hasUserInfo: false,
    hasCardInfo: false,
    canIUseGetUserProfile: false,
    isLoggingIn: false,
    shareImagePath: '',
    isAgreementChecked: false,
    needRefresh: false,
    isGeneratingShareImage: false,
    hasCompanyCode: false,
    companyInfo: null,
    enterpriseName: '企业',
    productsName: '产品中心',
    customName: '新页面', // 自定义页面名称
    customPage: '', // 自定义页面URL
    showContactPopup: false,
    isShowingTokenExpiredModal: false, // 标记是否正在显示token过期模态框
    aiAvatarUrl: '' // AI分身URL
  },

  onLoad() {
    console.log('个人页面 onLoad 被调用');

    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 初始化时同步登录状态
    this.syncLoginStatus();
  },

  onReady() {
    // Canvas准备就绪后，尝试生成分享图
    if (this.data.hasCardInfo) {
      this.generateShareImage()
    }
  },

  onUnload() {
    // 页面销毁时清理定时器
    if (this.tokenExpiredCheckTimer) {
      clearTimeout(this.tokenExpiredCheckTimer);
      this.tokenExpiredCheckTimer = null;
      console.log("页面销毁，清理token过期检查定时器");
    }
  },

  onShow() {
    console.log("onShow 方法被调用，当前状态：");
    console.log("app.globalData.userInfo:", app.globalData.userInfo);
    console.log("app.globalData.token:", app.globalData.token ? '存在' : '不存在');
    console.log("页面 hasUserInfo:", this.data.hasUserInfo);
    console.log("页面 isLoggingIn:", this.data.isLoggingIn);

    // 检查是否需要刷新
    if (this.data.needRefresh) {
      this.setData({
        needRefresh: false
      });
      // 重新获取名片信息
      this.getCardInfo();
      return;
    }

    // 检查自定义名称是否已更新
    if (app.globalData.customNamesUpdated) {
      // 清除标志
      app.globalData.customNamesUpdated = false;

      // 如果有企业信息，直接更新页面数据
      if (app.globalData.companyInfo) {
        this.setData({
          enterpriseName: app.globalData.companyInfo.enterpriseName || '企业',
          productsName: app.globalData.companyInfo.productsName || '产品中心'
        });

        // 尝试更新TabBar名称
        if (typeof this.getTabBar === 'function') {
          const tabBar = this.getTabBar();
          if (tabBar && typeof tabBar.updateNames === 'function') {
            tabBar.updateNames(
              app.globalData.companyInfo.enterpriseName || '企业',
              app.globalData.companyInfo.productsName || '产品中心'
            );
          }
        }
      } else {
        // 否则重新获取自定义名称
        this.getCustomNames();
      }
    }

    // 强制重新检查登录状态，确保页面状态与全局状态同步
    // 使用延迟确保页面完全加载后再同步状态
    setTimeout(() => {
      this.syncLoginStatus();
    }, 50);

    // 显示自定义tabBar，延迟执行确保页面已完全加载
    setTimeout(() => {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 0 // 选中个人页面
        });
      }
    }, 100);
  },

  // 同步登录状态的方法
  syncLoginStatus() {
    const storageToken = wx.getStorageSync('token');
    const storageUserInfo = wx.getStorageSync('userInfo');
    const globalToken = app.globalData.token;
    const globalUserInfo = app.globalData.userInfo;

    console.log("=== 同步登录状态详细信息 ===");
    console.log("存储中的token:", !!storageToken);
    console.log("全局token:", !!globalToken);
    console.log("存储中的userInfo:", !!storageUserInfo);
    console.log("全局userInfo:", !!globalUserInfo);
    console.log("当前页面hasUserInfo:", this.data.hasUserInfo);

    // 优先使用全局数据，如果全局数据不存在则从存储中恢复
    const finalToken = globalToken || storageToken;
    const finalUserInfo = globalUserInfo || storageUserInfo;

    if (finalToken && finalUserInfo) {
      console.log("检测到有效的登录信息，准备显示已登录状态");

      // 如果正在显示token过期模态框，取消显示并清理定时器
      if (this.data.isShowingTokenExpiredModal) {
        console.log("检测到用户已重新登录，取消token过期模态框显示");
        this.setData({
          isShowingTokenExpiredModal: false
        });

        // 清理可能存在的定时器
        if (this.tokenExpiredCheckTimer) {
          clearTimeout(this.tokenExpiredCheckTimer);
          this.tokenExpiredCheckTimer = null;
          console.log("已清理token过期检查定时器");
        }
      }

      // 确保全局状态正确
      if (!app.globalData.token && storageToken) {
        app.globalData.token = storageToken;
        console.log("从存储恢复全局token");
      }
      if (!app.globalData.userInfo && storageUserInfo) {
        app.globalData.userInfo = storageUserInfo;
        console.log("从存储恢复全局userInfo");
      }

      // 强制更新页面状态，即使当前已经是登录状态
      this.setData({
        userInfo: finalUserInfo,
        hasUserInfo: true,
        isLoggingIn: false,
        isShowingTokenExpiredModal: false // 确保重置模态框状态
      });

      console.log("页面状态已更新为已登录");

      // 获取名片信息（如果还没有的话）
      if (!this.data.cardInfo && !this.data.isLoggingIn) {
        console.log("准备获取名片信息");
        this.getCardInfo();
      } else if (app.globalData.cardUpdated) {
        console.log("名片已更新，重新获取");
        app.globalData.cardUpdated = false;
        this.getCardInfo();
      } else if (this.data.cardInfo) {
        console.log("名片信息已存在，刷新自定义名称");
        this.getCustomNames();
      }

      // 显示TabBar
      this.updateTabBarVisibility(true);

      console.log("✅ 登录状态同步完成，显示已登录界面");
    } else {
      console.log("未检测到有效的登录信息，显示未登录状态");

      // 清理页面状态
      this.setData({
        userInfo: null,
        hasUserInfo: false,
        cardInfo: null,
        hasCardInfo: false,
        hasCompanyCode: false,
        companyInfo: null,
        isLoggingIn: false
      });

      // 隐藏TabBar
      this.updateTabBarVisibility(false);

      console.log("✅ 登录状态同步完成，显示未登录界面");
    }
  },

  // 更新TabBar显示状态的辅助方法
  updateTabBarVisibility(show) {
    // 更新全局控制状态
    if (app.globalData.tabBarControl) {
      app.globalData.tabBarControl.show = show;
    } else {
      app.globalData.tabBarControl = {
        show: show,
        needCheckStatus: true
      };
    }
    
    // 直接更新TabBar组件（如果存在）
    setTimeout(() => {
      const tabBar = this.getTabBar();
      if (tabBar && typeof tabBar.setTabBarShow === 'function') {
        tabBar.setTabBarShow(show);
      }
    }, 50);
  },



  // 获取用户信息
  getUserInfo() {
    if (this.data.isLoggingIn) return

    this.setData({
      isLoggingIn: true
    })

    wx.showLoading({
      title: '加载中',
    })

    const auth = require('../../utils/auth.js')
    auth.getUserInfo().then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          userInfo: res.data,
          hasUserInfo: true
        })
        app.globalData.userInfo = res.data
        this.getCardInfo()

        // 用户登录成功后显示TabBar
        this.updateTabBarVisibility(true)
      }
      wx.hideLoading()
      this.setData({
        isLoggingIn: false
      })
    }).catch(err => {
      console.error('获取用户信息失败', err)

      // 如果是401错误，说明token已失效，统一处理
      if (err && (err.code === 401 || err.statusCode === 401)) {
        console.log('用户信息获取失败，token已失效')
        this.handleTokenExpired()
        return; // 直接返回，不继续执行后续逻辑
      }

      wx.hideLoading()
      this.setData({
        isLoggingIn: false
      })
    })
  },

  // 获取名片信息
  getCardInfo() {
    wx.showLoading({
      title: '加载中',
    })

    const card = require('../../utils/card.js')
    card.getMyCard().then(res => {
      console.log("获取到的名片信息:", res);

      if (res.code === 0 && res.data) {
        // 处理字段名不一致的问题
        const cardData = res.data;
        // 检查是否有company_code字段但没有companyCode字段的情况
        if (cardData.company_code !== undefined && cardData.companyCode === undefined) {
          cardData.companyCode = cardData.company_code;
          console.log("从company_code转换为companyCode:", cardData.companyCode);
        }

        console.log("处理后的名片数据:", cardData);

        this.setData({
          cardInfo: cardData,
          hasCardInfo: true,
          aiAvatarUrl: cardData.aiAvatarUrl || '' // 设置AI分身URL
        })

        // 打印关键信息以调试
        console.log("cardInfo.companyCode:", cardData.companyCode);
        console.log("cardInfo.aiAvatarUrl:", cardData.aiAvatarUrl);
        console.log("条件检查 hasCardInfo:", this.data.hasCardInfo);
        console.log("条件检查 cardInfo.companyCode:", this.data.cardInfo.companyCode);

        app.globalData.cardInfo = cardData

        if (cardData.companyCode) {
          this.setData({
            hasCompanyCode: true
          })
          // 获取企业信息
          this.getCompanyInfo(cardData.companyCode)
        }

        this.generateShareImage()
      } else {
        this.setData({
          hasCardInfo: false
        })
      }
      wx.hideLoading()
    }).catch(err => {
      console.error('获取名片信息失败', err)

      // 如果是401错误，说明token已失效，统一处理
      if (err && (err.code === 401 || err.statusCode === 401)) {
        console.log('名片信息获取失败，token已失效')
        this.handleTokenExpired()
        return; // 直接返回，不继续执行后续逻辑
      } else {
        this.setData({
          hasCardInfo: false
        })
      }

      wx.hideLoading()
    })
  },

  // 获取企业信息
  getCompanyInfo(companyCode) {
    const company = require('../../utils/company.js')
    company.getCompanyInfo(companyCode).then(res => {
      if (res) {
        // 清除之前的缓存
        if (app.globalData.companyInfo) {
          // 保存旧的自定义名称，以便检测是否有变化
          const oldEnterpriseName = app.globalData.companyInfo.enterpriseName;
          const oldProductsName = app.globalData.companyInfo.productsName;
          
          // 更新全局缓存
          app.globalData.companyInfo = res;
          
          // 检查自定义名称是否有变化
          if (res.enterpriseName !== oldEnterpriseName || res.productsName !== oldProductsName) {
            console.log('自定义名称已更新:', res.enterpriseName, res.productsName);
          }
        } else {
          // 首次加载，直接设置全局缓存
          app.globalData.companyInfo = res;
        }
        
        // 无论如何都更新页面数据
        this.setData({
          companyInfo: res,
          enterpriseName: res.enterpriseName || '企业',
          productsName: res.productsName || '产品中心'
        });
        
        // 尝试更新TabBar名称
        if (typeof this.getTabBar === 'function') {
          const tabBar = this.getTabBar();
          if (tabBar && typeof tabBar.updateNames === 'function') {
            tabBar.updateNames(
              res.enterpriseName || '企业',
              res.productsName || '产品中心'
            );
          }
        }
      }
    }).catch(err => {
      console.error('获取企业信息失败', err)

      // 如果是401错误，说明token已失效，不需要继续获取其他信息
      if (err && (err.code === 401 || err.statusCode === 401)) {
        console.log('企业信息获取失败，token已失效')
        return;
      }

      // 其他错误时尝试从store获取自定义名称
      this.getCustomNames();
    })
  },

  // 获取企业自定义名称
  getCustomNames() {
    // 如果全局数据中已有企业信息，直接使用
    if (app.globalData.companyInfo) {
      this.setData({
        enterpriseName: app.globalData.companyInfo.enterpriseName || '企业',
        productsName: app.globalData.companyInfo.productsName || '产品中心'
      });
      return;
    }
    
    // 如果没有企业信息，尝试获取
    const companyStore = require('../../store/company.js');
    companyStore.getCompanyCustomNames().then(res => {
      if (res.code === 0 && res.data) {
        this.setData({
          enterpriseName: res.data.enterpriseName || '企业',
          productsName: res.data.productsName || '产品中心'
        });
      }
    }).catch(err => {
      console.error('获取企业自定义名称失败', err);

      // 如果是401错误，说明token已失效，使用默认名称
      if (err && (err.code === 401 || err.statusCode === 401)) {
        console.log('企业自定义名称获取失败，token已失效，使用默认名称')
        this.setData({
          enterpriseName: '企业',
          productsName: '产品中心'
        });
      }
    });
  },

  // 处理token失效的统一方法
  handleTokenExpired() {
    console.log('处理token失效，清理状态并提示用户重新登录');

    // 如果已经在显示token过期模态框，不要重复显示
    if (this.data.isShowingTokenExpiredModal) {
      console.log('token过期模态框已经在显示，跳过重复显示');
      return;
    }

    // 清理页面状态
    this.setData({
      userInfo: null,
      hasUserInfo: false,
      cardInfo: null,
      hasCardInfo: false,
      hasCompanyCode: false,
      companyInfo: null,
      enterpriseName: '企业',
      productsName: '产品中心',
      isShowingTokenExpiredModal: true // 标记正在显示模态框
    });

    // 隐藏TabBar
    this.updateTabBarVisibility(false);

    // 延迟显示模态框，并在显示前再次检查登录状态
    this.tokenExpiredCheckTimer = setTimeout(() => {
      // 再次检查是否还需要显示模态框（用户可能已经重新登录）
      const currentToken = app.globalData.token || wx.getStorageSync('token');
      const currentUserInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');

      if (currentToken && currentUserInfo) {
        console.log('用户已重新登录，取消显示token过期模态框');
        this.setData({
          isShowingTokenExpiredModal: false
        });
        // 强制同步状态
        this.syncLoginStatus();
        return;
      }

      // 如果状态被重置，也不显示模态框
      if (!this.data.isShowingTokenExpiredModal) {
        console.log('模态框状态已被重置，取消显示');
        return;
      }

      // 提示用户重新登录
      wx.showModal({
        title: '登录已过期',
        content: '您的登录状态已过期，请重新登录',
        confirmText: '重新登录',
        confirmColor: '#3E7FFF',
        showCancel: false,
        success: (res) => {
          // 重置模态框状态
          this.setData({
            isShowingTokenExpiredModal: false
          });

          if (res.confirm) {
            this.goToLogin();
          }
        },
        fail: () => {
          // 模态框显示失败时也要重置状态
          this.setData({
            isShowingTokenExpiredModal: false
          });
        }
      });
    }, 200);
  },

  // 提示用户登录创建名片
  promptLogin() {
    wx.showModal({
      title: '需要登录',
      content: '您需要登录后才能使用此功能',
      confirmText: '去登录',
      confirmColor: '#3E7FFF',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin();
        }
      }
    });
  },
  
  // 未登录用户前往企业页面
  goToEnterpriseUnlogged() {
    wx.navigateTo({
      url: '/pages/enterprise/unlogged-display'
    });
  },
  
  // 未登录用户前往产品中心页面
  goToProductsUnlogged() {
    wx.navigateTo({
      url: '/pages/products/unlogged-display'
    });
  },
  
  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    });
  },

  // 修改toggleAgreement方法，移除但保留空方法
  toggleAgreement() {
    // 已移到登录页面
  },
  
  // 修改goToServiceAgreement方法，移除但保留空方法
  goToServiceAgreement() {
    // 已移到登录页面
  },
  
  // 修改goToPrivacyPolicy方法，移除但保留空方法
  goToPrivacyPolicy() {
    // 已移到登录页面
  },

  // 修改login方法，直接跳转到登录页面
  login() {
    this.goToLogin();
  },

  // 编辑名片
  editCard() {
    wx.navigateTo({
      url: '/pages/personal/edit',
    })
  },

  // 前往企业页面
  goToEnterprise() {
    // 无需检查链接是否存在，直接导航到企业页面，由企业页面自己处理各种情况
    // 传递当前用户的名片ID，确保分享时能正确携带信息
    let url = '/pages/enterprise/index';
    if (this.data.cardInfo && this.data.cardInfo.id) {
      url += `?cardId=${this.data.cardInfo.id}&fromPersonal=true`;
    }
    wx.navigateTo({
      url: url,
    })
  },

  // 跳转到产品中心
  goToProducts() {
    if (!this.data.hasCardInfo || !this.data.cardInfo || !this.data.cardInfo.companyCode) {
      wx.showToast({
        title: '您尚未关联企业',
        icon: 'none'
      })
      return
    }
    
    // 构建包含cardId的URL，确保从个人视角的产品中心分享时可以返回名片
    let url = `/pages/products/index?companyCode=${this.data.cardInfo.companyCode}`;
    
    // 添加cardId参数，确保能够返回到正确的名片
    if (this.data.cardInfo && this.data.cardInfo.id) {
      url += `&cardId=${this.data.cardInfo.id}`;
      // 增加fromPersonal标识，表示来自个人页面
      url += '&fromPersonal=true';
      console.log('跳转到产品中心，携带cardId:', this.data.cardInfo.id);
    }
    
    console.log('跳转到产品中心，URL:', url);
    
    wx.navigateTo({
      url: url
    })
  },

  // 跳转到AI分身页面
  goToAiAvatar() {
    if (!this.data.aiAvatarUrl) {
      wx.showToast({
        title: 'AI分身链接未设置',
        icon: 'none'
      })
      return
    }

    console.log('跳转到AI分身页面，URL:', this.data.aiAvatarUrl);

    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(this.data.aiAvatarUrl)}`
    })
  },

  // 跳转到自定义设置页面
  goToCustomSettings() {
    if (!this.data.hasUserInfo) {
      this.promptLogin();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/personal/custom-settings'
    });
  },

  // 显示退出登录确认对话框
  showLogoutConfirm() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmColor: '#3E7FFF',
      success: (res) => {
        if (res.confirm) {
          this.logout();
        }
      }
    })
  },
  
  // 退出登录
  logout() {
    // 显示加载提示
    wx.showLoading({
      title: '正在退出',
    })
    
    // 清除本地存储的登录信息
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 清除全局数据
    const app = getApp();
    app.globalData.userInfo = null;
    app.globalData.token = '';
    app.globalData.cardInfo = null;
    
    // 更新页面状态
    this.setData({
      userInfo: null,
      cardInfo: null,
      hasUserInfo: false,
      hasCardInfo: false
    });
    
    // 用户退出登录后隐藏TabBar
    app.globalData.tabBarControl = {
      show: false,
      needCheckStatus: true
    };
    
    // 更新TabBar组件
    this.updateTabBarVisibility(false);
    
    // 隐藏加载提示
    wx.hideLoading();
    
    // 显示退出成功提示
    wx.showToast({
      title: '已退出登录',
      icon: 'success',
      duration: 2000
    });
  },

  // 生成分享图
  generateShareImage() {
    if (!this.data.cardInfo) return
    
    // 设置正在生成分享图状态
    this.setData({
      isGeneratingShareImage: true
    });
    
    // 引入分享图生成工具
    const shareImage = require('../../utils/share-image.js');
    
    // 生成分享图
    shareImage.generateCardShareImage(this.data.cardInfo, 'shareCanvas')
      .then(tempFilePath => {
        this.setData({
          shareImagePath: tempFilePath,
          isGeneratingShareImage: false // 设置生成完成
        })
        console.log('生成分享图片成功', tempFilePath)
      })
      .catch(err => {
        console.error('生成分享图片失败', err)
        // 失败时尝试重试生成
        setTimeout(() => {
          this.retryGenerateShareImage()
        }, 1000)
      });
  },
  
  // 重试生成分享图 - 使用简化版本的分享图生成
  retryGenerateShareImage() {
    if (!this.data.cardInfo) return
    
    // 引入分享图生成工具
    const shareImage = require('../../utils/share-image.js');
    
    // 使用强制刷新参数调用工具函数
    shareImage.generateCardShareImage(this.data.cardInfo, 'shareCanvas', true)
      .then(tempFilePath => {
        this.setData({
          shareImagePath: tempFilePath,
          isGeneratingShareImage: false // 设置生成完成
        })
        console.log('重试生成分享图片成功', tempFilePath)
      })
      .catch(err => {
        console.error('重试生成分享图片仍然失败', err)
        // 确保释放生成状态，即使失败也允许用户点击分享
        this.setData({
          isGeneratingShareImage: false
        });
        
        // 使用默认图片作为最后的降级处理
        this.setData({
          shareImagePath: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png'
        });
      });
  },

  // 仅保留canvasToTempImage以处理特殊情况
  canvasToTempImage() {
    wx.canvasToTempFilePath({
      canvasId: 'shareCanvas',
      fileType: 'jpg',  // 使用JPG格式，没有透明通道
      quality: 0.9,     // 设置较高质量
      success: (res) => {
        this.setData({
          shareImagePath: res.tempFilePath,
          isGeneratingShareImage: false // 设置生成完成
        })
        console.log('生成分享图片成功', res.tempFilePath)
      },
      fail: (err) => {
        console.error('生成分享图片失败', err)
        // 失败时尝试重试生成
        setTimeout(() => {
          this.retryGenerateShareImage()
        }, 1000)
      }
    })
  },

  // 分享名片
  onShareAppMessage(options) {
    // 获取分享的来源按钮
    const shareType = options.target ? options.target.dataset.shareType : '';
    const cardId = this.data.cardInfo ? this.data.cardInfo.id : '';
    
    console.log("分享信息：", {
      shareType,
      cardId,
      cardInfo: this.data.cardInfo,
      companyInfo: this.data.companyInfo
    });
    
    // 使用统一分享模块
    const share = require('../../utils/share');
    
    // 检查是否有对应的企业代码
    if (shareType === 'enterprise' && (!this.data.companyInfo || !this.data.companyInfo.companyCode)) {
      console.log("企业代码未设置，不应该触发此分享");
      return share.getDefaultShare();
    }
    
    // 根据不同的分享类型处理
    if (shareType === 'enterprise') {
      // 分享企业
      return share.handleShare({
        type: 'enterprise',
        data: {
          cardInfo: this.data.cardInfo,
          companyInfo: this.data.companyInfo,
          enterpriseName: this.data.enterpriseName
        }
      });
    } else if (shareType === 'products') {
      // 分享产品中心
      return share.handleShare({
        type: 'products',
        data: {
          cardInfo: this.data.cardInfo,
          companyCode: this.data.cardInfo ? this.data.cardInfo.companyCode : '',
          productsName: this.data.productsName,
          productsShareImage: this.data.companyInfo && this.data.companyInfo.productsShareImage ? 
            this.data.companyInfo.productsShareImage : ''
        }
      });
    } else {
      // 默认分享名片
      return share.handleShare({
        type: 'card',
        data: {
          cardInfo: this.data.cardInfo,
          shareImagePath: this.data.shareImagePath
        }
      });
    }
  },

  // 创建新名片
  createNewCard() {
    wx.navigateTo({
      url: '/pages/personal/create-card'
    });
  },

  // 切换名片
  switchCard() {
    wx.navigateTo({
      url: '/pages/personal/switch-card'
    });
  },

  // 显示联系我们弹窗
  showContactPopup() {
    this.setData({
      showContactPopup: true
    });
  },
  
  // 关闭联系我们弹窗
  closeContactPopup() {
    this.setData({
      showContactPopup: false
    });
  },
}) 