Page({
  data: {
    url: '',
    loading: true,
    fromShare: false,
    cardId: null,
    isNavCollapsed: false, // 导航栏默认展开
    showNavBar: false, // 默认不显示导航栏
    companyCode: '', // 存储公司代码，用于返回产品中心
    productName: '', // 存储产品名称，用于分享
    productImage: '', // 存储产品图片，用于分享
    fromPersonal: false, // 标记是否来自个人页面
    companyInfo: null, // 企业信息
    customName: '新页面' // 自定义页面名称
  },

  onLoad(options) {
    console.log('产品详情页加载参数:', options);
    
    // 判断是否需要显示导航栏 - 现在所有情况都显示导航栏
    let shouldShowNavBar = true;

    // 处理fromShare参数，接受任何truthy值
    if (options.fromShare === 'true' || options.fromShare === true || options.fromShare === '1' || options.fromShare) {
      this.setData({
        fromShare: true
      });
      console.log('设置fromShare为true');
    }

    // 如果来自个人页面，也设置fromShare为true以显示返回名片按钮
    if (options.fromPersonal === 'true' || options.fromPersonal === true || options.fromPersonal) {
      this.setData({
        fromShare: true,
        fromPersonal: true
      });
      console.log('来自个人页面，设置fromShare为true以显示返回名片按钮');
    }

    // 现在所有情况都显示导航栏

    this.setData({
      showNavBar: shouldShowNavBar
    });
    
    // 保存卡片ID（如果有）
    if (options.cardId) {
      this.setData({
        cardId: options.cardId
      });
      console.log('设置cardId:', options.cardId);
    }
    
    // 保存公司代码（如果有）
    if (options.companyCode) {
      this.setData({
        companyCode: options.companyCode
      });
      console.log('设置companyCode:', options.companyCode);

      // 加载企业信息以获取自定义页面信息
      this.loadCompanyInfo(options.companyCode);
    }
    
    // 保存产品名称和图片（如果有）
    if (options.productName) {
      this.setData({
        productName: decodeURIComponent(options.productName)
      });
      console.log('设置productName:', decodeURIComponent(options.productName));
    }
    
    if (options.productImage) {
      this.setData({
        productImage: decodeURIComponent(options.productImage)
      });
      console.log('设置productImage:', decodeURIComponent(options.productImage));
    }
    
    if (options.url) {
      const decodedUrl = decodeURIComponent(options.url);
      this.setData({
        url: decodedUrl,
        loading: false
      });
      console.log('设置url:', decodedUrl);
    } else {
      wx.showToast({
        title: '产品链接无效',
        icon: 'none',
        duration: 2000
      });
      
      // 延迟返回
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
    
    // 额外调试信息
    console.log('导航栏显示状态:', {
      showNavBar: shouldShowNavBar,
      fromShare: this.data.fromShare,
      cardId: this.data.cardId,
      companyCode: this.data.companyCode
    });
  },

  // 网页加载完成
  onWebviewLoad() {
    this.setData({
      loading: false
    });
  },

  // 网页加载失败
  onWebviewError(e) {
    console.error('网页加载失败', e.detail);
    this.setData({
      loading: false
    });
    wx.showToast({
      title: '页面加载失败',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 显示导航栏
  showNav() {
    console.log('显示导航栏');
    this.setData({
      isNavCollapsed: false
    });
  },
  
  // 隐藏导航栏
  hideNav() {
    console.log('隐藏导航栏');
    this.setData({
      isNavCollapsed: true
    });
  },
  
  // 保留原来的toggleNav以防其他地方调用
  toggleNav() {
    console.log('切换导航栏状态，当前状态:', this.data.isNavCollapsed);
    this.setData({
      isNavCollapsed: !this.data.isNavCollapsed
    });
    console.log('切换后状态:', this.data.isNavCollapsed);
  },
  
  // 返回名片页面
  goBackToCard() {
    if (this.data.cardId) {
      wx.navigateTo({
        url: `/pages/card-detail/index?id=${this.data.cardId}`,
        fail: (err) => {
          console.error("跳转到名片页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: `/pages/card-detail/index?id=${this.data.cardId}`
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到原始名片',
        icon: 'none',
        duration: 2000
      });
    }
  },
  
  // 返回产品中心
  goBackToProducts() {
    if (this.data.companyCode) {
      let url = `/pages/products/index?companyCode=${this.data.companyCode}`;
      
      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }
      
      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }
      
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到产品中心失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 返回个人页面
  goBackToPersonal() {
    wx.switchTab({
      url: '/pages/personal/index'
    });
  },

  // 加载企业信息
  loadCompanyInfo(companyCode) {
    const company = require('../../utils/company.js');
    company.getCompanyInfo(companyCode)
      .then(res => {
        console.log('获取到的企业信息:', res);

        // 检查返回的是标准响应格式还是直接的企业信息对象
        let companyData = res;

        // 如果是标准响应格式（有code和data字段）
        if (res && res.code === 0 && res.data) {
          companyData = res.data;
        }

        // 如果获取到有效的企业信息
        if (companyData && companyData.companyCode) {
          this.setData({
            companyInfo: companyData,
            customName: companyData.customName || '新页面'
          });
        }
      })
      .catch(err => {
        console.error('获取企业信息失败:', err);
      });
  },

  // 跳转到自定义页面
  goToCustomPage() {
    if (this.data.companyCode) {
      let url = `/pages/custom/index?companyCode=${this.data.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到自定义页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 跳转到企业页面
  goToEnterprise() {
    if (this.data.companyCode) {
      let url = `/pages/enterprise/index?companyCode=${this.data.companyCode}`;

      // 如果有卡片ID，也一并带上
      if (this.data.cardId) {
        url += `&cardId=${this.data.cardId}`;
      }

      // 如果是从分享进入的，传递fromShare参数
      if (this.data.fromShare) {
        url += `&fromShare=true`;
      }

      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error("跳转到企业页面失败:", err);
          // 如果跳转失败，尝试使用redirectTo
          wx.redirectTo({
            url: url
          });
        }
      });
    } else {
      wx.showToast({
        title: '未找到企业信息',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 分享功能
  onShareAppMessage() {
    // 使用统一分享模块
    const share = require('../../utils/share');
    
    console.log('准备分享产品，当前数据:', {
      url: this.data.url,
      productName: this.data.productName,
      productImage: this.data.productImage,
      cardId: this.data.cardId,
      companyCode: this.data.companyCode,
      fromShare: this.data.fromShare,
      fromPersonal: this.data.fromPersonal
    });
    
    // 准备分享数据
    const shareData = {
      productUrl: this.data.url,
      productName: this.data.productName || '产品详情', // 使用保存的产品名称
      productImage: this.data.productImage || 'https://pic.sdtaa.com/ZhiLian/Picture/Project/default-share.png', // 使用保存的产品图片
      cardId: this.data.cardId, // 保留cardId，确保能返回名片
      companyCode: this.data.companyCode, // 保留companyCode，确保能查看产品中心
      fromPersonal: this.data.fromPersonal // 保留fromPersonal标识
    };
    
    // 如果在分享数据中没有指定cardId或companyCode，尝试从页面参数中获取
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options || {};
    
    if (!shareData.cardId && options.cardId) {
      shareData.cardId = options.cardId;
      console.log('从页面参数中获取cardId:', options.cardId);
    }
    
    if (!shareData.companyCode && options.companyCode) {
      shareData.companyCode = options.companyCode;
      console.log('从页面参数中获取companyCode:', options.companyCode);
    }
    
    // 如果没有设置fromPersonal，尝试从页面参数中获取
    if (!shareData.fromPersonal && options.fromPersonal) {
      shareData.fromPersonal = true;
      console.log('从页面参数中获取fromPersonal标识');
    }
    
    console.log('最终分享数据:', shareData);
    
    // 默认分享产品详情
    return share.handleShare({
      type: 'product',
      data: shareData
    });
  }
}); 